#ifndef __CS_CONFIG_H__
#define __CS_CONFIG_H__
/** @file cs_config.h
 ****************************************************************************
 *
 * @brief
 *     This module allows individual features in the API to be compiled
 *     in or out to manage code space.
 *
 ****************************************************************************
 * <AUTHOR>    This file contains information proprietary to Inphi Corporation, Inc.
 *    (Inphi). Any use or disclosure, in whole or in part, of this
 *    information to any unauthorized party, for any purposes other than that for
 *    which it is provided is expressly prohibited except as authorized by
 *    Inphi in writing. Inphi reserves its rights to pursue both civil and
 *    criminal penalties for copying or disclosure of this material without
 *    authorization. Inphi Corporation (R), INPHI (R) and the Inphi Logo
 *    are the trademarks or registered trademarks of Inphi Corporation, Inc.,
 *    and its subsidiaries in the U.S. and other countries. Any other
 *    product and company names are the trademarks of their respective owners.
 *
 *    Copyright (C) 2006-2015 Inphi Corporation, Inc. All rights reserved.
 *
 *    API Version Number: 3.7.8
 ***************************************************************************/

/* Set to enable compile in debug features such as register dump */
#define CS_HAS_DEBUG_LOOPBACKS      1
#define CS_HAS_DEBUG_PRBS           1
#define CS_HAS_DEBUG_REGISTER_DUMPS 1
#define CS_HAS_DEBUG_STATUS_DUMPS   1

/* Set to define where the CS_TRACE output goes (stderr or stdout)
 * This is ignored if CS_DONT_USE_STDLIB is defined */
#define CS_TRACE_STREAM stdout

/* Set to allow the use of floating-point math */
#define CS_HAS_FLOATING_POINT 

/* Set to include the Interrupt handling code */
/* #define CS_HAS_INTERRUPTS  */

/* Set to not use stdlib */
/* #define CS_DONT_USE_STDLIB */

/* Set to not use inlining */
/* #define CS_DONT_USE_INLINE */

/* Set to include filesystem  */
/* #define CS_HAS_FILESYSTEM  */

/* Set to not load the ucode automatically (dangerous) */
/*#define CS_SKIP_UCODE_DOWNLOAD */

/* Set to enable experimental maddr masking (dangerous) */
/* #define CS_MULTI_CHIP_UCODE_PRGM */

#endif /* __CS_CONFIG_H__ */
