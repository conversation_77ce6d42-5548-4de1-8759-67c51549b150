
#include "cs4224_api.h"
#include "CS4343_operation.h"
#include "Drivers.h"
#include "BER_Test.h"
#include "Lcd_Interface.h"
/**
 * The following example describes the process of configuring
 * the device to support 1.25G operation
 */

cs_status Rules_Set_1g(void)
{
	  cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;
//    e_cs4224_polarity_inv_t slice_inv[] = {  \
//        /*0*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_HOST_TX) , \
//        /*1*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
//        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
//        /*3*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
//        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
//        /*5*/ CS4224_POLARITY_INV_NONE , \
//        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_RX, \
//        /*7*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_RX | CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
//    };
//	    e_cs4224_polarity_inv_t slice_inv[] = {  \
//        /*0*/ CS4224_POLARITY_INV_DPLX_LINE_TX, \
//        /*1*/ CS4224_POLARITY_INV_NONE, \
//        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_TX , \
//        /*3*/ CS4224_POLARITY_INV_NONE , \
//        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_TX , \
//        /*5*/ CS4224_POLARITY_INV_NONE , \
//        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_TX, \
//        /*7*/ CS4224_POLARITY_INV_NONE , \
//    };
    status |= cs4224_hard_reset(slice);
    if(CS_OK != status)
    {
      //CS_TRACE(("ERROR trying to reset device\n"));
        return status;
    }

    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		rules.ref_clk_rate                     = (float)f_si570[rate]/rdiv[DIV_Rate[rate].rdivpos]*ddiv[DIV_Rate[rate].ddivpos];//CS4224_REF_CLK_155p52;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = DIV_Rate[rate].ddivpos;//CS4224_CLKDIV_RDIV_BY_64;
    rules.clkdiv.rdiv = DIV_Rate[rate].rdivpos;
		    /* Assume an SR transceiver */
    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;

    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_CX1;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
		
		for(slice = 2; slice < 6; slice=slice+2)
    {
//      if(polarity_ch[slice/2])  
//			{
//				rules.enable_fec   = TRUE; 
//				rules.polarity_inv = slice_inv[slice];
//        /* Initialize the slice and bring it into operational state */
//        status |=cs4224_slice_enter_operational_state(slice, &rules);
//			}
//			else
				status |= cs4224_slice_enter_operational_state(slice, &rules);
    }
		cs4224_slice_power_down(0);
		cs4224_slice_power_down(1);
		cs4224_slice_power_down(3);
		cs4224_slice_power_down(5);
		cs4224_slice_power_down(6);
		cs4224_slice_power_down(7);
    return status;
}


cs_status cs4343_init_1g(int chan)
{
    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;

//    status |= cs4224_hard_reset(slice);
//    if(CS_OK != status)
//    {
//      //CS_TRACE(("ERROR trying to reset device\n"));
//        return status;
//    }

    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_1G, &rules);
		
		    /* Assume an SR transceiver */
    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;

    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
//    
//    for(slice = 0; slice < 8; slice=slice+2)
//    {
        status |= cs4224_slice_enter_operational_state(chan*2, &rules);
//    }
    return status;
}
cs_status cs4343_init_1g_inv(int chan)
{
    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;
    e_cs4224_polarity_inv_t slice_inv[] = {  \
        /*0*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_HOST_TX) , \
        /*1*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*3*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*5*/ CS4224_POLARITY_INV_NONE , \
        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_RX, \
        /*7*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_RX | CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
    };
		
//    status |= cs4224_hard_reset(slice);
//    if(CS_OK != status)
//    {
//      //CS_TRACE(("ERROR trying to reset device\n"));
//        return status;
//    }

    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_1G, &rules);
		
		    /* Assume an SR transceiver */
    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;

    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
		
		rules.enable_fec   = TRUE;
    
//    for(slice = 0; slice < 8; slice=slice+2)
//    {
      rules.polarity_inv = slice_inv[chan*2];  
			status |= cs4224_slice_enter_operational_state(chan*2, &rules);
//    }
    return status;
}


cs_status cs4343_init_5g()
{
    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;

 //CS_PRINTF(("Initializing the chip in 5g mode\n"));

//    status |= cs4224_hard_reset(slice);
//    if(CS_OK != status)
//    {
//      //CS_TRACE(("ERROR trying to reset device\n"));
//        return status;
//    }
 
    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_5G, &rules);
    
    for(slice = 0; slice < CS4224_MAX_NUM_SLICES(0); slice=slice+2)
    {
        status |= cs4224_slice_enter_operational_state(slice, &rules);
    }

    return status;
}

cs_status cs4343_init_7p5g()
{
    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;

 //CS_PRINTF(("Initializing the chip in 7.5g mode\n"));

//    status |= cs4224_hard_reset(slice);
//    if(CS_OK != status)
//    {
//      //CS_TRACE(("ERROR trying to reset device\n"));
//        return status;
//    }
 
    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_7p5G, &rules);
    
    for(slice = 0; slice < CS4224_MAX_NUM_SLICES(0); slice=slice+2)
    {
        status |= cs4224_slice_enter_operational_state(slice, &rules);
    }

    return status;
}

cs_status Rules_Set_XGpon()
{
	  cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;

		
    status |= cs4224_hard_reset(slice);
    if(CS_OK != status)
    {
      //CS_TRACE(("ERROR trying to reset device\n"));
        return status;
    }		

    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		//rules.ref_clk_rate                     = 156.25;
		rules.ref_clk_rate                     = 155.52;
		rules.clkdiv.enable = TRUE;

 
    /* Assume an SR transceiver */
    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;

    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_CX1;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB; 

    rules.enable_fec   = TRUE;
		
		
		if(mdiochan == 0)
		{
				rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_4;
				rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_64; 
				status |= cs4224_slice_enter_operational_state(2, &rules);
				
				rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_1;
				rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_64; 
				status |= cs4224_slice_enter_operational_state(4, &rules);
		}
		else
		{
				rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_1;
				rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_64; 
				status |= cs4224_slice_enter_operational_state(2, &rules);
				
				rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_4;
				rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_64; 
				status |= cs4224_slice_enter_operational_state(4, &rules);
		}
	
		cs4224_slice_power_down(0);
		cs4224_slice_power_down(1);
		cs4224_slice_power_down(3);
		cs4224_slice_power_down(5);
		cs4224_slice_power_down(6);
		cs4224_slice_power_down(7);	

    return status;	
}

cs_status Rules_Set_Gpon()
{
	  cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;

		
    status |= cs4224_hard_reset(slice);
    if(CS_OK != status)
    {
      //CS_TRACE(("ERROR trying to reset device\n"));
        return status;
    }		

    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		//rules.ref_clk_rate                     = 156.25;
		rules.ref_clk_rate                     = 155.52;
		rules.clkdiv.enable = TRUE;

 
    /* Assume an SR transceiver */
    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;

    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_CX1;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB; 

    rules.enable_fec   = TRUE;
		
		
		if(mdiochan == 0)
		{
				rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_8;
				rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_64; 
				status |= cs4224_slice_enter_operational_state(2, &rules);
				
				rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_4;
				rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_64; 
				status |= cs4224_slice_enter_operational_state(4, &rules);
		}
		else
		{
				rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_8;
				rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_64; 
				status |= cs4224_slice_enter_operational_state(2, &rules);
				
				rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_4;
				rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_64; 
				status |= cs4224_slice_enter_operational_state(4, &rules);
		}
	
		cs4224_slice_power_down(0);
		cs4224_slice_power_down(1);
		cs4224_slice_power_down(3);
		cs4224_slice_power_down(5);
		cs4224_slice_power_down(6);
		cs4224_slice_power_down(7);	

    return status;	
}

cs_status Rules_Set_Epon()
{
	  cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;

		
    status |= cs4224_hard_reset(slice);
    if(CS_OK != status)
    {
      //CS_TRACE(("ERROR trying to reset device\n"));
        return status;
    }		

    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		rules.ref_clk_rate                     = 156.25;
		//rules.ref_clk_rate                     = 155.52;
		rules.clkdiv.enable = TRUE;

 
    /* Assume an SR transceiver */
    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;

    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_CX1;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB; 

    rules.enable_fec   = TRUE;
		
		
		if(mdiochan == 0)
		{
				rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_8;
				rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_64; 
				status |= cs4224_slice_enter_operational_state(2, &rules);
				
				rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_1;
				rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_66; 
				status |= cs4224_slice_enter_operational_state(4, &rules);
		}
		else
		{
				rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_1;
				rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_66; 
				status |= cs4224_slice_enter_operational_state(2, &rules);
				
				rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_8;
				rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_64; 
				status |= cs4224_slice_enter_operational_state(4, &rules);
		}
	
		cs4224_slice_power_down(0);
		cs4224_slice_power_down(1);
		cs4224_slice_power_down(3);
		cs4224_slice_power_down(5);
		cs4224_slice_power_down(6);
		cs4224_slice_power_down(7);	

    return status;	
}

cs_status Rules_Set_Cpon()
{
	  cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;

		
    status |= cs4224_hard_reset(slice);
    if(CS_OK != status)
    {
      //CS_TRACE(("ERROR trying to reset device\n"));
        return status;
    }		

    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		//rules.ref_clk_rate                     = 156.25;
		rules.ref_clk_rate                     = 155.52;
		rules.clkdiv.enable = TRUE;

 
    /* Assume an SR transceiver */
    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;

    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_CX1;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB; 

    rules.enable_fec   = TRUE;
		
		
		if(mdiochan == 0)
		{
				rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_4;
				rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_64; 
				status |= cs4224_slice_enter_operational_state(2, &rules);
				
				rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_8;
				rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_64; 
				status |= cs4224_slice_enter_operational_state(4, &rules);
		}
		else
		{
				rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_1;
				rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_64; 
				status |= cs4224_slice_enter_operational_state(2, &rules);
				
				rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_4;
				rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_64; 
				status |= cs4224_slice_enter_operational_state(4, &rules);
		}
	
		cs4224_slice_power_down(0);
		cs4224_slice_power_down(1);
		cs4224_slice_power_down(3);
		cs4224_slice_power_down(5);
		cs4224_slice_power_down(6);
		cs4224_slice_power_down(7);	

    return status;	
}

cs_status Rules_Set_XGSpon()
{
	  cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;

		
    status |= cs4224_hard_reset(slice);
    if(CS_OK != status)
    {
      //CS_TRACE(("ERROR trying to reset device\n"));
        return status;
    }		

    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		//rules.ref_clk_rate                     = 156.25;
		rules.ref_clk_rate                     = 155.52;
		rules.clkdiv.enable = TRUE;

 
    /* Assume an SR transceiver */
    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;

    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_CX1;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB; 

    rules.enable_fec   = TRUE;
		
		
		if(mdiochan == 0)
		{
				rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_4;
				rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_64; 
				status |= cs4224_slice_enter_operational_state(2, &rules);
				
				rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_8;
				rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_64; 
				status |= cs4224_slice_enter_operational_state(4, &rules);
		}
		else
		{
				rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_1;
				rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_64; 
				status |= cs4224_slice_enter_operational_state(2, &rules);
				
				rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_1;
				rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_64; 
				status |= cs4224_slice_enter_operational_state(4, &rules);
		}
	
		cs4224_slice_power_down(0);
		cs4224_slice_power_down(1);
		cs4224_slice_power_down(3);
		cs4224_slice_power_down(5);
		cs4224_slice_power_down(6);
		cs4224_slice_power_down(7);	

    return status;	
}

/**
 * The following example describes the process of configuring
 * the device to support 10.3125G operation
 *
 * @return CS_OK on success, CS_ERROR on failure
 */
cs_status Rules_Set_10g()
{
	  cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;
//    e_cs4224_polarity_inv_t slice_inv[] = {  \
//        /*0*/ CS4224_POLARITY_INV_DPLX_LINE_TX, \
//        /*1*/ CS4224_POLARITY_INV_NONE, \
//        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_TX , \
//        /*3*/ CS4224_POLARITY_INV_NONE , \
//        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_TX , \
//        /*5*/ CS4224_POLARITY_INV_NONE , \
//        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_TX, \
//        /*7*/ CS4224_POLARITY_INV_NONE , \
//    };
//	    e_cs4224_polarity_inv_t slice_inv[] = {  \
//        /*0*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_HOST_TX) , \
//        /*1*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
//        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
//        /*3*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
//        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
//        /*5*/ CS4224_POLARITY_INV_NONE , \
//        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_RX, \
//        /*7*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_RX | CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
//    };
		
    status |= cs4224_hard_reset(slice);
    if(CS_OK != status)
    {
      //CS_TRACE(("ERROR trying to reset device\n"));
        return status;
    }		

    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
//		rules.ref_clk_rate                     = 156.25;

//		rules.clkdiv.enable = TRUE;
//		rules.clkdiv.fastdiv = 0x3; /* div by 40 (default) */
//		rules.clkdiv.ddiv    = CS4224_DDIV_DIV1;
//		rules.clkdiv.rdiv    = CS4224_RDIV_DIV66;	
		rules.ref_clk_rate                     = (float)f_si570[rate]/rdiv[DIV_Rate[rate].rdivpos]*ddiv[DIV_Rate[rate].ddivpos];//CS4224_REF_CLK_155p52;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = DIV_Rate[rate].ddivpos;//CS4224_CLKDIV_RDIV_BY_64;
    rules.clkdiv.rdiv = DIV_Rate[rate].rdivpos;


 
    /* Assume an SR transceiver */
    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_CX1;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;

    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_CX1;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB; 

    	

		
		for(slice = 2; slice < 6; slice=slice+2)
    {
//      if(polarity_ch[slice/2])  
//			{
//				rules.enable_fec   = TRUE; 					
//				rules.polarity_inv = slice_inv[slice];				
//        /* Initialize the slice and bring it into operational state */
//        status |=cs4224_slice_enter_operational_state(slice, &rules);
//			}
//			else
				status |= cs4224_slice_enter_operational_state(slice, &rules);
    }	
		cs4224_slice_power_down(0);
		cs4224_slice_power_down(1);
		cs4224_slice_power_down(3);
		cs4224_slice_power_down(5);
		cs4224_slice_power_down(6);
		cs4224_slice_power_down(7);

    return status;	
}

cs_status Rules_Set_Rate()
{
	  cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;
//    e_cs4224_polarity_inv_t slice_inv[] = {  \
//        /*0*/ CS4224_POLARITY_INV_DPLX_LINE_TX, \
//        /*1*/ CS4224_POLARITY_INV_NONE, \
//        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_TX , \
//        /*3*/ CS4224_POLARITY_INV_NONE , \
//        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_TX , \
//        /*5*/ CS4224_POLARITY_INV_NONE , \
//        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_TX, \
//        /*7*/ CS4224_POLARITY_INV_NONE , \
//    };
//	    e_cs4224_polarity_inv_t slice_inv[] = {  \
//        /*0*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_HOST_TX) , \
//        /*1*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
//        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
//        /*3*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
//        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
//        /*5*/ CS4224_POLARITY_INV_NONE , \
//        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_RX, \
//        /*7*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_RX | CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
//    };
		
    status |= cs4224_hard_reset(slice);
    if(CS_OK != status)
    {
      //CS_TRACE(("ERROR trying to reset device\n"));
        return status;
    }		

    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
//		rules.ref_clk_rate                     = 156.25;

//		rules.clkdiv.enable = TRUE;
//		rules.clkdiv.fastdiv = 0x3; /* div by 40 (default) */
//		rules.clkdiv.ddiv    = CS4224_DDIV_DIV1;
//		rules.clkdiv.rdiv    = CS4224_RDIV_DIV66;	
		rules.ref_clk_rate                     = def_rate/rdiv[DIV_userRate[userRatepos].rdivpos]*ddiv[DIV_userRate[userRatepos].ddivpos];//CS4224_REF_CLK_155p52;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = DIV_userRate[userRatepos].ddivpos;//DIV_Rate[0].ddivpos;//CS4224_CLKDIV_RDIV_BY_64;
    rules.clkdiv.rdiv = DIV_userRate[userRatepos].rdivpos;//DIV_Rate[0].rdivpos;
//		rules.ref_clk_rate                     = def_rate/rdiv[DIV_Rate[rate].rdivpos]*ddiv[DIV_Rate[rate].ddivpos];//CS4224_REF_CLK_155p52;
//		rules.clkdiv.enable = TRUE;
//		rules.clkdiv.ddiv = DIV_Rate[rate].ddivpos;//DIV_Rate[0].ddivpos;//CS4224_CLKDIV_RDIV_BY_64;
//    rules.clkdiv.rdiv = DIV_Rate[rate].rdivpos;//DIV_Rate[0].rdivpos;


 
    /* Assume an SR transceiver */
    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_CX1;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;

    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_CX1;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB; 

    	

		
		for(slice = 0; slice < 8; slice=slice+2)
    {
//      if(polarity_ch[slice/2])  
//			{
//				rules.enable_fec   = TRUE; 					
//				rules.polarity_inv = slice_inv[slice];				
//        /* Initialize the slice and bring it into operational state */
//        status |=cs4224_slice_enter_operational_state(slice, &rules);
//			}
//			else
				status |= cs4224_slice_enter_operational_state(slice, &rules);
    }	

    return status;	
}



cs_status cs4343_init_10g(int chan)
{
    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;

//    status |= cs4224_hard_reset(slice);
//    if(CS_OK != status)
//    {
//      //CS_TRACE(("ERROR trying to reset device\n"));
//        return status;
//    }		

    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		rules.ref_clk_rate                     = 128.90625;
//      rules.ref_clk_rate = 156.25;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.fastdiv = 0x3; /* div by 40 (default) */
		rules.clkdiv.ddiv    = CS4224_DDIV_DIV1;
		rules.clkdiv.rdiv    = CS4224_RDIV_DIV80;	
////		rules.fracdiv.enable                   = FALSE; 
////	  rules.clkdiv.enable = TRUE;
////		rules.clkdiv.fastdiv = 0x5; /* div by 66 */ 
////		rules.clkdiv.ddiv    = CS4224_DDIV_DIV1;
////		rules.clkdiv.rdiv    = CS4224_RDIV_DIV64;  
//		
////				rules.ref_clk_rate                     = 164.35546875;

//		rules.clkdiv.enable = TRUE;
//		rules.clkdiv.fastdiv = 0x3; /* div by 40 (default) */
//		rules.clkdiv.ddiv    = CS4224_DDIV_DIV1;
//		rules.clkdiv.rdiv    = CS4224_RDIV_DIV64;

 
    /* Assume an SR transceiver */
    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;

    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;    

		
//		for(slice = 0; slice < 8; slice=slice+2)
//    {
        status |= cs4224_slice_enter_operational_state(chan*2, &rules);
//    }

    return status;
}


/**
 * The following example describes the process of configuring
 * the device to support 10.3125G operation
 *
 * @return CS_OK on success, CS_ERROR on failure
 */
cs_status cs4343_init_10g_inv(int chan)
{
    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;
    /* polarity mapping for 8 slice duplex part CS4343:
     * Slice  |  inversions
     *     0  |  Line Rx & Host Tx
     *     1  |  Line Rx
     *     2  |  None
     *     3  |  Host Tx, Line Rx, Line Tx
     *     4  |  None
     *     5  |  None
     *     6  |  Line Rx, Line Tx
     *     7  |  Host Rx, Host Tx, Line Rx, Line Tx
     */
    e_cs4224_polarity_inv_t slice_inv[] = {  \
        /*0*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_HOST_TX) , \
        /*1*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*3*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*5*/ CS4224_POLARITY_INV_NONE , \
        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_RX, \
        /*7*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_RX | CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
    };
    

 //CS_PRINTF(("Initializing the chip in 10.3125G mode\n"));

    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. Only the upper 24 bits of the
       slice parameter are used to reference the appropriate ASIC. The
       lower 8 bits are ignored. This method should only be called once
       when the ASIC is first powered on. */
//    status |= cs4224_hard_reset(slice);
//    if(CS_OK != status)
//    {
//      //CS_TRACE(("ERROR trying to reset device\n"));
//        return status;
//    }

    /* Provision all ports of the chip for 10.3125G mode. This
     * assumes:
     *   - Device configured for 156.25MHz reference clock
     *   - No fractional divider
     *   - Microcode programmed automatically if not
     *     already programmed via EEPROM.
     * The rules_set_default() method does not actually configure
     * the ASIC, it just defaults the 'rules' structure for
     * the desired application.
     */ 
    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		
		rules.ref_clk_rate                     = 128.90625;

		rules.clkdiv.enable = TRUE;
		rules.clkdiv.fastdiv = 0x3; /* div by 40 (default) */
		rules.clkdiv.ddiv    = CS4224_DDIV_DIV1;
		rules.clkdiv.rdiv    = CS4224_RDIV_DIV80;	

    /* Assume an SR transceiver */
    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;

    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
    /* FEC requires the correct polarity */
    rules.enable_fec   = TRUE;

    for(slice = 0; slice < 8; slice=slice+2)
    {
        rules.polarity_inv = slice_inv[chan*2];
        /* Initialize the slice and bring it into operational state */
        cs4224_slice_enter_operational_state(chan*2, &rules);
    }

    return status;
}

cs_status Rules_Set_10gwan()
{
	    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;
//	    e_cs4224_polarity_inv_t slice_inv[] = {  \
//        /*0*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_HOST_TX) , \
//        /*1*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
//        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
//        /*3*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
//        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
//        /*5*/ CS4224_POLARITY_INV_NONE , \
//        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_RX, \
//        /*7*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_RX | CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
//    };
//    e_cs4224_polarity_inv_t slice_inv[] = {  \
//        /*0*/ CS4224_POLARITY_INV_DPLX_LINE_TX, \
//        /*1*/ CS4224_POLARITY_INV_NONE, \
//        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_TX , \
//        /*3*/ CS4224_POLARITY_INV_NONE , \
//        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_TX , \
//        /*5*/ CS4224_POLARITY_INV_NONE , \
//        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_TX, \
//        /*7*/ CS4224_POLARITY_INV_NONE , \
//    };
    status |= cs4224_hard_reset(slice);
    if(CS_OK != status)
    {
      //CS_TRACE(("ERROR trying to reset device\n"));
        return status;
    }
 
    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);		

		rules.ref_clk_rate                     = (float)f_si570[rate]/rdiv[DIV_Rate[rate].rdivpos]*ddiv[DIV_Rate[rate].ddivpos];//CS4224_REF_CLK_155p52;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = DIV_Rate[rate].ddivpos;//CS4224_CLKDIV_RDIV_BY_64;
    rules.clkdiv.rdiv = DIV_Rate[rate].rdivpos;//CS4224_CLKDIV_RDIV_BY_64; 		

    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_CX1;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;

    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_CX1;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
		for(slice = 2; slice < 6; slice=slice+2)
    {
//      if(polarity_ch[slice/2])  
//			{
//				rules.enable_fec   = TRUE; 
//				rules.polarity_inv = slice_inv[slice];
//        /* Initialize the slice and bring it into operational state */
//        status |=cs4224_slice_enter_operational_state(slice, &rules);
//			}
//			else
				status |= cs4224_slice_enter_operational_state(slice, &rules);
    }
		cs4224_slice_power_down(0);
		cs4224_slice_power_down(1);
		cs4224_slice_power_down(3);
		cs4224_slice_power_down(5);
		cs4224_slice_power_down(6);
		cs4224_slice_power_down(7);

    return status;	
	
}



cs_status Rules_Set_Cpri155()
{
	    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;
//	    e_cs4224_polarity_inv_t slice_inv[] = {  \
//        /*0*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_HOST_TX) , \
//        /*1*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
//        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
//        /*3*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
//        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
//        /*5*/ CS4224_POLARITY_INV_NONE , \
//        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_RX, \
//        /*7*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_RX | CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
//    };
//    e_cs4224_polarity_inv_t slice_inv[] = {  \
//        /*0*/ CS4224_POLARITY_INV_DPLX_LINE_TX, \
//        /*1*/ CS4224_POLARITY_INV_NONE, \
//        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_TX , \
//        /*3*/ CS4224_POLARITY_INV_NONE , \
//        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_TX , \
//        /*5*/ CS4224_POLARITY_INV_NONE , \
//        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_TX, \
//        /*7*/ CS4224_POLARITY_INV_NONE , \
//    };
    status |= cs4224_hard_reset(slice);
    if(CS_OK != status)
    {
      //CS_TRACE(("ERROR trying to reset device\n"));
        return status;
    }
 
    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);		

		rules.ref_clk_rate                     = CS4224_REF_CLK_155p52;//(float)f_si570[rate]/rdiv[DIV_Rate[rate].rdivpos]*ddiv[DIV_Rate[rate].ddivpos];//
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_64;//DIV_Rate[rate].ddivpos;//
    rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_64; 	//DIV_Rate[rate].rdivpos;//	

    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;

    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_CX1;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
		for(slice = 2; slice < 6; slice=slice+2)
    {
//      if(polarity_ch[slice/2])  
//			{
//				rules.enable_fec   = TRUE; 
//				rules.polarity_inv = slice_inv[slice];
//        /* Initialize the slice and bring it into operational state */
//        status |=cs4224_slice_enter_operational_state(slice, &rules);
//			}
//			else
				status |= cs4224_slice_enter_operational_state(slice, &rules);
    }
		cs4224_slice_power_down(0);
		cs4224_slice_power_down(1);
		cs4224_slice_power_down(3);
		cs4224_slice_power_down(5);
		cs4224_slice_power_down(6);
		cs4224_slice_power_down(7);

    return status;	
	
}

cs_status Rules_Set_Cpri622()
{
	    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;
//	    e_cs4224_polarity_inv_t slice_inv[] = {  \
//        /*0*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_HOST_TX) , \
//        /*1*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
//        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
//        /*3*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
//        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
//        /*5*/ CS4224_POLARITY_INV_NONE , \
//        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_RX, \
//        /*7*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_RX | CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
//    };
//    e_cs4224_polarity_inv_t slice_inv[] = {  \
//        /*0*/ CS4224_POLARITY_INV_DPLX_LINE_TX, \
//        /*1*/ CS4224_POLARITY_INV_NONE, \
//        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_TX , \
//        /*3*/ CS4224_POLARITY_INV_NONE , \
//        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_TX , \
//        /*5*/ CS4224_POLARITY_INV_NONE , \
//        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_TX, \
//        /*7*/ CS4224_POLARITY_INV_NONE , \
//    };
    status |= cs4224_hard_reset(slice);
    if(CS_OK != status)
    {
      //CS_TRACE(("ERROR trying to reset device\n"));
        return status;
    }
 
    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);		

		rules.ref_clk_rate                     = (float)f_si570[rate]/rdiv[DIV_Rate[rate].rdivpos]*ddiv[DIV_Rate[rate].ddivpos];//CS4224_REF_CLK_155p52;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = DIV_Rate[rate].ddivpos;//CS4224_CLKDIV_RDIV_BY_64;
    rules.clkdiv.rdiv = DIV_Rate[rate].rdivpos;//CS4224_CLKDIV_RDIV_BY_64; 		

    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;

    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_CX1;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
		for(slice = 2; slice < 6; slice=slice+2)
    {
//      if(polarity_ch[slice/2])  
//			{
//				rules.enable_fec   = TRUE; 
//				rules.polarity_inv = slice_inv[slice];
//        /* Initialize the slice and bring it into operational state */
//        status |=cs4224_slice_enter_operational_state(slice, &rules);
//			}
//			else
				status |= cs4224_slice_enter_operational_state(slice, &rules);
    }
		cs4224_slice_power_down(0);
		cs4224_slice_power_down(1);
		cs4224_slice_power_down(3);
		cs4224_slice_power_down(5);
		cs4224_slice_power_down(6);
		cs4224_slice_power_down(7);

    return status;	
	
}

cs_status Rules_Set_Cpri3()
{
	    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;
//	    e_cs4224_polarity_inv_t slice_inv[] = {  \
//        /*0*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_HOST_TX) , \
//        /*1*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
//        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
//        /*3*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
//        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
//        /*5*/ CS4224_POLARITY_INV_NONE , \
//        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_RX, \
//        /*7*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_RX | CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
//    };
//    e_cs4224_polarity_inv_t slice_inv[] = {  \
//        /*0*/ CS4224_POLARITY_INV_DPLX_LINE_TX, \
//        /*1*/ CS4224_POLARITY_INV_NONE, \
//        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_TX , \
//        /*3*/ CS4224_POLARITY_INV_NONE , \
//        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_TX , \
//        /*5*/ CS4224_POLARITY_INV_NONE , \
//        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_TX, \
//        /*7*/ CS4224_POLARITY_INV_NONE , \
//    };
    status |= cs4224_hard_reset(slice);
    if(CS_OK != status)
    {
      //CS_TRACE(("ERROR trying to reset device\n"));
        return status;
    }
 
    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);		

		rules.ref_clk_rate                     = (float)f_si570[rate]/rdiv[DIV_Rate[rate].rdivpos]*ddiv[DIV_Rate[rate].ddivpos];//CS4224_REF_CLK_155p52;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = DIV_Rate[rate].ddivpos;//CS4224_CLKDIV_RDIV_BY_64;
    rules.clkdiv.rdiv = DIV_Rate[rate].rdivpos;//CS4224_CLKDIV_RDIV_BY_64; 		

    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;

    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_CX1;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
		for(slice = 2; slice < 6; slice=slice+2)
    {
//      if(polarity_ch[slice/2])  
//			{
//				rules.enable_fec   = TRUE; 
//				rules.polarity_inv = slice_inv[slice];
//        /* Initialize the slice and bring it into operational state */
//        status |=cs4224_slice_enter_operational_state(slice, &rules);
//			}
//			else
				status |= cs4224_slice_enter_operational_state(slice, &rules);
    }
		cs4224_slice_power_down(0);
		cs4224_slice_power_down(1);
		cs4224_slice_power_down(3);
		cs4224_slice_power_down(5);
		cs4224_slice_power_down(6);
		cs4224_slice_power_down(7);

    return status;	
	
}

cs_status Rules_Set_Cpri6()
{
	    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;
//	    e_cs4224_polarity_inv_t slice_inv[] = {  \
//        /*0*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_HOST_TX) , \
//        /*1*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
//        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
//        /*3*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
//        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
//        /*5*/ CS4224_POLARITY_INV_NONE , \
//        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_RX, \
//        /*7*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_RX | CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
//    };
//    e_cs4224_polarity_inv_t slice_inv[] = {  \
//        /*0*/ CS4224_POLARITY_INV_DPLX_LINE_TX, \
//        /*1*/ CS4224_POLARITY_INV_NONE, \
//        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_TX , \
//        /*3*/ CS4224_POLARITY_INV_NONE , \
//        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_TX , \
//        /*5*/ CS4224_POLARITY_INV_NONE , \
//        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_TX, \
//        /*7*/ CS4224_POLARITY_INV_NONE , \
//    };
    status |= cs4224_hard_reset(slice);
    if(CS_OK != status)
    {
      //CS_TRACE(("ERROR trying to reset device\n"));
        return status;
    }
 
    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);		

		rules.ref_clk_rate                     = (float)f_si570[rate]/rdiv[DIV_Rate[rate].rdivpos]*ddiv[DIV_Rate[rate].ddivpos];//CS4224_REF_CLK_155p52;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = DIV_Rate[rate].ddivpos;//CS4224_CLKDIV_RDIV_BY_64;
    rules.clkdiv.rdiv = DIV_Rate[rate].rdivpos;//CS4224_CLKDIV_RDIV_BY_64; 		

    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;

    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_CX1;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
		for(slice = 2; slice < 6; slice=slice+2)
    {
//      if(polarity_ch[slice/2])  
//			{
//				rules.enable_fec   = TRUE; 
//				rules.polarity_inv = slice_inv[slice];
//        /* Initialize the slice and bring it into operational state */
//        status |=cs4224_slice_enter_operational_state(slice, &rules);
//			}
//			else
				status |= cs4224_slice_enter_operational_state(slice, &rules);
    }
		cs4224_slice_power_down(0);
		cs4224_slice_power_down(1);
		cs4224_slice_power_down(3);
		cs4224_slice_power_down(5);
		cs4224_slice_power_down(6);
		cs4224_slice_power_down(7);

    return status;	
	
}


cs_status Rules_Set_Cpri9()
{
	    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;

    status |= cs4224_hard_reset(slice);
    if(CS_OK != status)
    {
      //CS_TRACE(("ERROR trying to reset device\n"));
        return status;
    }
 
    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);		

		rules.ref_clk_rate                     = (float)f_si570[rate]/rdiv[DIV_Rate[rate].rdivpos]*ddiv[DIV_Rate[rate].ddivpos];//CS4224_REF_CLK_155p52;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = DIV_Rate[rate].ddivpos;//CS4224_CLKDIV_RDIV_BY_64;
    rules.clkdiv.rdiv = DIV_Rate[rate].rdivpos;//CS4224_CLKDIV_RDIV_BY_64; 		

    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;

    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_CX1;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
		for(slice = 2; slice < 6; slice=slice+2)
    {
				status |= cs4224_slice_enter_operational_state(slice, &rules);
    }
		cs4224_slice_power_down(0);
		cs4224_slice_power_down(1);
		cs4224_slice_power_down(3);
		cs4224_slice_power_down(5);
		cs4224_slice_power_down(6);
		cs4224_slice_power_down(7);

    return status;	
	
}

cs_status Rules_Set_Cpri300()
{
	    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;

    status |= cs4224_hard_reset(slice);
    if(CS_OK != status)
    {
      //CS_TRACE(("ERROR trying to reset device\n"));
        return status;
    }
 
    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);		

		rules.ref_clk_rate                     = 150.0;//(float)f_si570[rate]/rdiv[DIV_Rate[rate].rdivpos]*ddiv[DIV_Rate[rate].ddivpos];//CS4224_REF_CLK_155p52;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = DIV_Rate[rate].ddivpos;//CS4224_CLKDIV_RDIV_BY_64;
    rules.clkdiv.rdiv = DIV_Rate[rate].rdivpos;//CS4224_CLKDIV_RDIV_BY_64; 		

    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;

    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_CX1;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
		for(slice = 2; slice < 6; slice=slice+2)
    {
				status |= cs4224_slice_enter_operational_state(slice, &rules);
    }
		cs4224_slice_power_down(0);
		cs4224_slice_power_down(1);
		cs4224_slice_power_down(3);
		cs4224_slice_power_down(5);
		cs4224_slice_power_down(6);
		cs4224_slice_power_down(7);

    return status;	
	
}


cs_status cs4343_init_10gwan(int chan)
{
    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;

//    status |= cs4224_hard_reset(slice);
//    if(CS_OK != status)
//    {
//      //CS_TRACE(("ERROR trying to reset device\n"));
//        return status;
//    }
 
    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);		

		rules.ref_clk_rate                     = CS4224_REF_CLK_155p52;
		rules.clkdiv.enable = TRUE;
    rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_64; 		

    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;

    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
//    for(slice = 0; slice < 8; slice=slice+2)
//    {
        status |= cs4224_slice_enter_operational_state(chan*2, &rules);
//    }

    return status;	
}

cs_status cs4343_init_10gwan_inv(int chan)
{
    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;
    e_cs4224_polarity_inv_t slice_inv[] = {  \
        /*0*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_HOST_TX) , \
        /*1*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*3*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*5*/ CS4224_POLARITY_INV_NONE , \
        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_RX, \
        /*7*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_RX | CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
    };
		
//    status |= cs4224_hard_reset(slice);
//    if(CS_OK != status)
//    {
//      //CS_TRACE(("ERROR trying to reset device\n"));
//        return status;
//    }
 
    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);		

		rules.ref_clk_rate                     = CS4224_REF_CLK_155p52;
		rules.clkdiv.enable = TRUE;
    rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_64; 		

    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;

    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
		
		rules.enable_fec   = TRUE;
    
    
		  rules.polarity_inv = slice_inv[chan*2];  
			status |= cs4224_slice_enter_operational_state(chan*2, &rules);
//		for(slice = 0; slice < 8; slice=slice+2)
//    {
//      rules.polarity_inv = slice_inv[slice];  
//			status |= cs4224_slice_enter_operational_state(slice, &rules);
//    }

    return status;	
}
/**
 * The following example describes the process of configuring
 * the device to support 15.0G operation
 *
 * @return CS_OK on success, CS_ERROR on failure
 */

cs_status cs4343_init_15g()
{
    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;

//    status |= cs4224_hard_reset(slice);
//    if(CS_OK != status)
//    {
//      //CS_TRACE(("ERROR trying to reset device\n"));
//        return status;
//    }

    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		//rules.fracdiv.enable                   = FALSE;
		rules.ref_clk_rate                     = 117.1875;
		rules.clkdiv.enable = TRUE;
    rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_128; 
 
    /* Assume an SR transceiver */
    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_6dB;

    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;		            
//			rules.rx_if.splx_edc_mode              = CS_HSIO_EDC_MODE_15G_BP;
//			rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_15G_BP;
//			rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_15G_BP;

//			rules.rx_if.splx_eq.traceloss          = CS_HSIO_TRACE_LOSS_6dB;
//			rules.tx_if.splx_driver.traceloss      = CS_HSIO_TRACE_LOSS_27dB;

//			rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_6dB;
//			rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_6dB;
//			rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_27dB;
//			rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_27dB;
		
    
    for(slice = 0; slice < CS4224_MAX_NUM_SLICES(0); slice=slice+2)
    {
        status |= cs4224_slice_enter_operational_state(slice, &rules);
    }

    return status;
}

cs_status Rules_Set_15g()
{
	    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;

//    e_cs4224_polarity_inv_t slice_inv[] = {  \
//        /*0*/ CS4224_POLARITY_INV_DPLX_LINE_TX, \
//        /*1*/ CS4224_POLARITY_INV_NONE, \
//        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_TX , \
//        /*3*/ CS4224_POLARITY_INV_NONE , \
//        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_TX , \
//        /*5*/ CS4224_POLARITY_INV_NONE , \
//        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_TX, \
//        /*7*/ CS4224_POLARITY_INV_NONE , \
//    };
    status |= cs4224_hard_reset(slice);
    if(CS_OK != status)
    {
      //CS_TRACE(("ERROR trying to reset device\n"));
        return status;
    }
 
    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
 
//				rules.ref_clk_rate                     = 150;
//		rules.clkdiv.enable = TRUE;
//		 rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_1;
//    rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_100; 
		
		rules.ref_clk_rate                     = (float)f_si570[rate]/rdiv[DIV_Rate[rate].rdivpos]*ddiv[DIV_Rate[rate].ddivpos];//CS4224_REF_CLK_155p52;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = DIV_Rate[rate].ddivpos;//CS4224_CLKDIV_RDIV_BY_64;
    rules.clkdiv.rdiv = DIV_Rate[rate].rdivpos;
    /* Assume an SR transceiver */
    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_CX1;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;

    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_CX1;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
		
		rules.enable_fec   = TRUE;
    
		for(slice = 2; slice < 6; slice=slice+2)
    {
//      if(polarity_ch[slice/2])  
//			{
//				rules.enable_fec   = TRUE; 
//				rules.polarity_inv = slice_inv[slice];
//        /* Initialize the slice and bring it into operational state */
//        status |=cs4224_slice_enter_operational_state(slice, &rules);
//			}
//			else
				status |= cs4224_slice_enter_operational_state(slice, &rules);
    }
		cs4224_slice_power_down(0);
		cs4224_slice_power_down(1);
		cs4224_slice_power_down(3);
		cs4224_slice_power_down(5);
		cs4224_slice_power_down(6);
		cs4224_slice_power_down(7);

    return status;	
	
}

cs_status cs4343_init_15g_inv()
{
    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;
    e_cs4224_polarity_inv_t slice_inv[] = {  \
        /*0*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_HOST_TX) , \
        /*1*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*3*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*5*/ CS4224_POLARITY_INV_NONE , \
        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_RX, \
        /*7*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_RX | CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
    };
		
//    status |= cs4224_hard_reset(slice);
//    if(CS_OK != status)
//    {
//      //CS_TRACE(("ERROR trying to reset device\n"));
//        return status;
//    }

    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_15G, &rules);
 
    /* Assume an SR transceiver */
    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;

    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
		
		rules.enable_fec   = TRUE;
    
    for(slice = 0; slice < CS4224_MAX_NUM_SLICES(0); slice=slice+2)
    {
      rules.polarity_inv = slice_inv[slice];  
			status |= cs4224_slice_enter_operational_state(slice, &rules);
    }

    return status;
}

cs_status cs4343_init_fcan()
{
    cs_status status = CS_OK;
    cs_uint32 slice = 0;
    e_cs4224_fcan_an_status_t an_done[8]  = 
        {CS4224_FCAN_AN_NOT_DONE, CS4224_FCAN_AN_NOT_DONE, 
         CS4224_FCAN_AN_NOT_DONE, CS4224_FCAN_AN_NOT_DONE,
         CS4224_FCAN_AN_NOT_DONE, CS4224_FCAN_AN_NOT_DONE, 
         CS4224_FCAN_AN_NOT_DONE, CS4224_FCAN_AN_NOT_DONE};
    e_cs4224_fcan_data_rate_t an_rates[8] =
        {CS4224_FCAN_DATA_RATE_DISABLED, CS4224_FCAN_DATA_RATE_DISABLED,
         CS4224_FCAN_DATA_RATE_DISABLED, CS4224_FCAN_DATA_RATE_DISABLED,
         CS4224_FCAN_DATA_RATE_DISABLED, CS4224_FCAN_DATA_RATE_DISABLED,
         CS4224_FCAN_DATA_RATE_DISABLED, CS4224_FCAN_DATA_RATE_DISABLED};

    cs4224_rules_t rules;

    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. Only the upper 24 bits of the
       slice parameter are used to reference the appropriate ASIC. The
       lower 8 bits are ignored. This method should only be called once
       when the ASIC is first powered on. */
//    status |= cs4224_hard_reset(slice);
//    if(CS_OK != status)
//    {
//      //CS_TRACE(("ERROR trying to reset device\n"));
//        return status;
//    }

    /* Setup the rules for FC-AN */
    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_FCAN, &rules);

    /* Advertise the data-rate capabilities
     * Note: Some restrictions apply to the advertised rates. They are:
     *   - No more than 3 rates can be advertised at one time.
     *   - The 3 advertised rates must be in series such as
     *       1G, 2G, 4G
     *       2G, 4G, 8G
     *       4G, 8G, 16G
     */
    rules.fcan.data_rates = CS4224_FCAN_DATA_RATE_16G | 
                            CS4224_FCAN_DATA_RATE_8G  |
                            CS4224_FCAN_DATA_RATE_4G;

    rules.ref_clk_rate                      = 106.25;

    /* Advertized capabilities */
    rules.fcan.speed_negotiation_support    = TRUE; 
    rules.fcan.training_protocol_support    = TRUE;  /* applies to 16G FC only */
    rules.fcan.fec_capable                  = TRUE;  /* applies to 16G FC only */
    rules.fcan.fec_request                  = TRUE;  /* applies to 16G FC only */
    rules.fcan.transmitter_fixed            = FALSE; /* applies to 16G FC only */

    /* SR and FCAN DFE modes are supported on the line side.
     * 8G and 16G data-rates are automaticaly configured for DFE mode, data-rates
     * lower than 8G automatically configured for SR mode.   
     */
    rules.rx_if.dplx_line_edc_mode          = CS_HSIO_EDC_MODE_FCAN;
    /* SR and DFE modes are supported on the host side */
    rules.rx_if.dplx_host_edc_mode          = CS_HSIO_EDC_MODE_SR;

    /* trace loss settings */
    rules.tx_if.dplx_line_driver.traceloss  = CS_HSIO_TRACE_LOSS_0dB;
    rules.tx_if.dplx_host_driver.traceloss  = CS_HSIO_TRACE_LOSS_0dB;
    rules.rx_if.dplx_line_eq.traceloss      = CS_HSIO_TRACE_LOSS_0dB;
    rules.rx_if.dplx_host_eq.traceloss      = CS_HSIO_TRACE_LOSS_0dB;

    /* when configuring multiple slices at once, it's more efficient to not
     * have enter_operational_state wait for FC-AN to complete. Instead,
     * we will manually use the wait_for_an method after configuring.
     */
    rules.fcan.wait_for_an_done             = FALSE;

    /* configure all (duplex) slices */
    for(slice = 0; slice < CS4224_MAX_NUM_SLICES(0); slice=slice+2)
    {
        status |= cs4224_slice_enter_operational_state(slice, &rules);
    }

    /* poll all slices for AN to complete and get the results, if any */
    for (slice = 0; slice < CS4224_MAX_NUM_SLICES(0); slice=slice+2)
    {
        /* block for 10 seconds waiting for AN to complete */
        an_done[slice] = cs4224_fcan_wait_for_an_with_timeout(slice, 10, &(an_rates[slice]));
        /* or block forever waiting for AN to complete */
        /* an_done[slice] = cs4224_fcan_wait_for_an(slice, &(an_rates[slice]));*/
    }

    /* configure all slices to their negotiated results, if available */
    for (slice = 0; slice < CS4224_MAX_NUM_SLICES(0); slice=slice+2)
    {
        if (an_done[slice] == CS4224_FCAN_AN_DONE)
        {
            rules.fcan.negotiated_rate = an_rates[slice];
            status |= cs4224_fcan_init_fc_post_an(slice, &rules);
        }
        else
        {
            /* dump error message indicating slice did not complete AN */
          //CS_TRACE(("ERROR: FCAN did not complete on slice %d",slice));
            status |= CS_ERROR;
        }
    }

    return status;
}

cs_status Rules_Set_rxaui(void)
{
	  cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;
//	   e_cs4224_polarity_inv_t slice_inv[] = {  \
//        /*0*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_HOST_TX) , \
//        /*1*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
//        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
//        /*3*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
//        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
//        /*5*/ CS4224_POLARITY_INV_NONE , \
//        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_RX, \
//        /*7*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_RX | CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
//    };
//	    e_cs4224_polarity_inv_t slice_inv[] = {  \
//        /*0*/ CS4224_POLARITY_INV_DPLX_LINE_TX, \
//        /*1*/ CS4224_POLARITY_INV_NONE, \
//        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_TX , \
//        /*3*/ CS4224_POLARITY_INV_NONE , \
//        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_TX , \
//        /*5*/ CS4224_POLARITY_INV_NONE , \
//        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_TX, \
//        /*7*/ CS4224_POLARITY_INV_NONE , \
//    };
    //CS_PRINTF(("Initializing the chip in rxaui 6.250G mode\n"));

    status |= cs4224_hard_reset(slice);
    if(CS_OK != status)
    {
      //CS_TRACE(("ERROR trying to reset device\n"));
        return status;
    }
 
    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		
//		rules.fracdiv.enable = FALSE;

//		rules.clkdiv.enable = TRUE;
//    rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_2;
//		rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_80;//CS4224_CLKDIV_RDIV_BY_40 is bug
		rules.ref_clk_rate                     = (float)f_si570[rate]/rdiv[DIV_Rate[rate].rdivpos]*ddiv[DIV_Rate[rate].ddivpos];//CS4224_REF_CLK_155p52;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = DIV_Rate[rate].ddivpos;//CS4224_CLKDIV_RDIV_BY_64;
    rules.clkdiv.rdiv = DIV_Rate[rate].rdivpos;
		
		/* Assume an SR transceiver */
    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_CX1;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_CX1;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
		for(slice = 2; slice < 6; slice=slice+2)
    {
//      if(polarity_ch[slice/2])  
//			{
//				rules.enable_fec   = TRUE; 
//				rules.polarity_inv = slice_inv[slice];
//        /* Initialize the slice and bring it into operational state */
//        status |=cs4224_slice_enter_operational_state(slice, &rules);
//			}
//			else
				status |= cs4224_slice_enter_operational_state(slice, &rules);
    }
		cs4224_slice_power_down(0);
		cs4224_slice_power_down(1);
		cs4224_slice_power_down(3);
		cs4224_slice_power_down(5);
		cs4224_slice_power_down(6);
		cs4224_slice_power_down(7);

    return status;	
	
}

cs_status cs4343_init_rxaui(int chan)
{
    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;

    //CS_PRINTF(("Initializing the chip in rxaui 6.250G mode\n"));

//    status |= cs4224_hard_reset(slice);
//    if(CS_OK != status)
//    {
//      //CS_TRACE(("ERROR trying to reset device\n"));
//        return status;
//    }
 
    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		
		rules.fracdiv.enable = FALSE;

		rules.clkdiv.enable = TRUE;
    rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_2;
		rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_80;//CS4224_CLKDIV_RDIV_BY_40 is bug
		
		
		/* Assume an SR transceiver */
    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
//    for(slice = 0; slice < 8; slice=slice+2)
//    {
        status |= cs4224_slice_enter_operational_state(chan*2, &rules);
//    }

    return status;		
}

cs_status cs4343_init_rxaui_inv(int chan)
{
    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;

    //CS_PRINTF(("Initializing the chip in rxaui 6.250G mode\n"));
	   e_cs4224_polarity_inv_t slice_inv[] = {  \
        /*0*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_HOST_TX) , \
        /*1*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*3*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*5*/ CS4224_POLARITY_INV_NONE , \
        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_RX, \
        /*7*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_RX | CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
    };

//    status |= cs4224_hard_reset(slice);
//    if(CS_OK != status)
//    {
//      //CS_TRACE(("ERROR trying to reset device\n"));
//        return status;
//    }
 
    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		
		rules.fracdiv.enable = FALSE;

		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_2;
		rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_80;//CS4224_CLKDIV_RDIV_BY_40 is bug
		
		/* Assume an SR transceiver */
    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;

    /* FEC requires the correct polarity */
    rules.enable_fec   = TRUE;
    
//    for(slice = 0; slice < 8; slice=slice+2)
//    {
      rules.polarity_inv = slice_inv[chan*2];  
			status |= cs4224_slice_enter_operational_state(chan*2, &rules);
//    }

    return status;		
}

cs_status Rules_Set_xaui()
{
    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;
//		e_cs4224_polarity_inv_t slice_inv[] = {  \
//        /*0*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_HOST_TX) , \
//        /*1*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
//        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
//        /*3*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
//        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
//        /*5*/ CS4224_POLARITY_INV_NONE , \
//        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_RX, \
//        /*7*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_RX | CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
//    };
//	    e_cs4224_polarity_inv_t slice_inv[] = {  \
//        /*0*/ CS4224_POLARITY_INV_DPLX_LINE_TX, \
//        /*1*/ CS4224_POLARITY_INV_NONE, \
//        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_TX , \
//        /*3*/ CS4224_POLARITY_INV_NONE , \
//        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_TX , \
//        /*5*/ CS4224_POLARITY_INV_NONE , \
//        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_TX, \
//        /*7*/ CS4224_POLARITY_INV_NONE , \
//    };
    //CS_PRINTF(("Initializing the chip in xaui 3.125G mode\n"));

    status |= cs4224_hard_reset(slice);
    if(CS_OK != status)
    {
      //CS_TRACE(("ERROR trying to reset device\n"));
        return status;
    }
 
    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		
//		rules.fracdiv.enable = FALSE;

//		rules.clkdiv.enable = TRUE;
//		rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_4;
//		rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_80;
    rules.ref_clk_rate                     = (float)f_si570[rate]/rdiv[DIV_Rate[rate].rdivpos]*ddiv[DIV_Rate[rate].ddivpos];//CS4224_REF_CLK_155p52;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = DIV_Rate[rate].ddivpos;//CS4224_CLKDIV_RDIV_BY_64;
    rules.clkdiv.rdiv = DIV_Rate[rate].rdivpos;
		
		/* Assume an SR transceiver */
    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_CX1;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
		for(slice = 2; slice < 6; slice=slice+2)
    {
//      if(polarity_ch[slice/2])  
//			{
//				rules.enable_fec   = TRUE; 
//				rules.polarity_inv = slice_inv[slice];
//        /* Initialize the slice and bring it into operational state */
//        status |=cs4224_slice_enter_operational_state(slice, &rules);
//			}
//			else
				status |= cs4224_slice_enter_operational_state(slice, &rules);
    }
		cs4224_slice_power_down(0);
		cs4224_slice_power_down(1);
		cs4224_slice_power_down(3);
		cs4224_slice_power_down(5);
		cs4224_slice_power_down(6);
		cs4224_slice_power_down(7);

    return status;	
}


cs_status cs4343_init_xaui(int chan)
{
    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;

    //CS_PRINTF(("Initializing the chip in xaui 3.125G mode\n"));

//    status |= cs4224_hard_reset(slice);
//    if(CS_OK != status)
//    {
//      //CS_TRACE(("ERROR trying to reset device\n"));
//        return status;
//    }
 
    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		
		rules.fracdiv.enable = FALSE;

		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_4;
		rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_80;

		
		/* Assume an SR transceiver */
    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
//    for(slice = 0; slice < 8; slice=slice+2)
//    {
        status |= cs4224_slice_enter_operational_state(chan*2, &rules);
//    }

    return status;		
}

cs_status cs4343_init_xaui_inv(int chan)
{
    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;
	   e_cs4224_polarity_inv_t slice_inv[] = {  \
        /*0*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_HOST_TX) , \
        /*1*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*3*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*5*/ CS4224_POLARITY_INV_NONE , \
        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_RX, \
        /*7*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_RX | CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
    };

    //CS_PRINTF(("Initializing the chip in xaui 3.125G mode\n"));

//    status |= cs4224_hard_reset(slice);
//    if(CS_OK != status)
//    {
//      //CS_TRACE(("ERROR trying to reset device\n"));
//        return status;
//    }
 
    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		
		rules.fracdiv.enable = FALSE;

		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_4;
		rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_80;
		
		/* Assume an SR transceiver */
    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
    rules.enable_fec   = TRUE;
		
//		for(slice = 0; slice < 8; slice=slice+2)
//    {
      rules.polarity_inv = slice_inv[chan*2];  
			status |= cs4224_slice_enter_operational_state(chan*2, &rules);
//    }

    return status;		
}

cs_status cs4343_init_oc192()
{
    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;

    //CS_PRINTF(("Initializing the chip in oc96 mode\n"));

//    status |= cs4224_hard_reset(slice);
//    if(CS_OK != status)
//    {
//      //CS_TRACE(("ERROR trying to reset device\n"));
//        return status;
//    }
 
    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);		

		rules.fracdiv.enable = FALSE;
		rules.ref_clk_rate                     = CS4224_REF_CLK_155p52;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_1;
		rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_64;

		/* Assume an SR transceiver */
    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
    for(slice = 0; slice < CS4224_MAX_NUM_SLICES(0); slice=slice+2)
    {
        status |= cs4224_slice_enter_operational_state(slice, &rules);
    }

    return status;	
}

cs_status cs4343_init_oc192_inv()
{
    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;
    e_cs4224_polarity_inv_t slice_inv[] = {  \
        /*0*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_HOST_TX) , \
        /*1*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*3*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*5*/ CS4224_POLARITY_INV_NONE , \
        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_RX, \
        /*7*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_RX | CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
    };
    //CS_PRINTF(("Initializing the chip in oc96 mode\n"));

//    status |= cs4224_hard_reset(slice);
//    if(CS_OK != status)
//    {
//      //CS_TRACE(("ERROR trying to reset device\n"));
//        return status;
//    }
 
    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);		

		rules.fracdiv.enable = FALSE;
		rules.ref_clk_rate                     = CS4224_REF_CLK_155p52;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_1;
		rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_64;

		/* Assume an SR transceiver */
    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
		
		rules.enable_fec   = TRUE;
    
    for(slice = 0; slice < CS4224_MAX_NUM_SLICES(0); slice=slice+2)
    {
      rules.polarity_inv = slice_inv[slice];  
			status |= cs4224_slice_enter_operational_state(slice, &rules);
    }

    return status;	
}

cs_status Rules_Set_oc96()
{
    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;
//    e_cs4224_polarity_inv_t slice_inv[] = {  \
//        /*0*/ CS4224_POLARITY_INV_DPLX_LINE_TX, \
//        /*1*/ CS4224_POLARITY_INV_NONE, \
//        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_TX , \
//        /*3*/ CS4224_POLARITY_INV_NONE , \
//        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_TX , \
//        /*5*/ CS4224_POLARITY_INV_NONE , \
//        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_TX, \
//        /*7*/ CS4224_POLARITY_INV_NONE , \
//    };
    //CS_PRINTF(("Initializing the chip in oc96 mode\n"));

    status |= cs4224_hard_reset(slice);
    if(CS_OK != status)
    {
      //CS_TRACE(("ERROR trying to reset device\n"));
        return status;
    }
 
    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		
//		rules.fracdiv.enable = TRUE;
//		rules.fracdiv.numerator = 0xf66e86;
//    rules.fracdiv.divisor = 0x7;
//		rules.fracdiv.enable = FALSE;
//		rules.ref_clk_rate                     = CS4224_REF_CLK_155p52;
//		rules.clkdiv.enable = TRUE;
//		rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_2;
//		rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_64;
		rules.ref_clk_rate                     = (float)f_si570[rate]/rdiv[DIV_Rate[rate].rdivpos]*ddiv[DIV_Rate[rate].ddivpos];//CS4224_REF_CLK_155p52;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = DIV_Rate[rate].ddivpos;//CS4224_CLKDIV_RDIV_BY_64;
    rules.clkdiv.rdiv = DIV_Rate[rate].rdivpos;
		
		/* Assume an SR transceiver */
    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_CX1;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
		
		rules.enable_fec   = TRUE;
    
		for(slice = 2; slice < 6; slice=slice+2)
    {
//      if(polarity_ch[slice/2])  
//			{
//				rules.enable_fec   = TRUE; 
//				rules.polarity_inv = slice_inv[slice];
//        /* Initialize the slice and bring it into operational state */
//        status |=cs4224_slice_enter_operational_state(slice, &rules);
//			}
//			else
				status |= cs4224_slice_enter_operational_state(slice, &rules);
    }
		cs4224_slice_power_down(0);
		cs4224_slice_power_down(1);
		cs4224_slice_power_down(3);
		cs4224_slice_power_down(5);
		cs4224_slice_power_down(6);
		cs4224_slice_power_down(7);

    return status;	
}

cs_status cs4343_init_oc96(int chan)
{
    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;

    //CS_PRINTF(("Initializing the chip in oc96 mode\n"));

//    status |= cs4224_hard_reset(slice);
//    if(CS_OK != status)
//    {
//      //CS_TRACE(("ERROR trying to reset device\n"));
//        return status;
//    }
 
    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		
//		rules.fracdiv.enable = TRUE;
//		rules.fracdiv.numerator = 0xf66e86;
//    rules.fracdiv.divisor = 0x7;
		rules.fracdiv.enable = FALSE;
		rules.ref_clk_rate                     = CS4224_REF_CLK_155p52;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_2;
		rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_64;
		
		/* Assume an SR transceiver */
    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
//    for(slice = 0; slice < 8; slice=slice+2)
//    {
        status |= cs4224_slice_enter_operational_state(chan*2, &rules);
//    }

    return status;	
}

cs_status cs4343_init_oc96_inv(int chan)
{
    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;
    e_cs4224_polarity_inv_t slice_inv[] = {  \
        /*0*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_HOST_TX) , \
        /*1*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*3*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*5*/ CS4224_POLARITY_INV_NONE , \
        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_RX, \
        /*7*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_RX | CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
    };
    //CS_PRINTF(("Initializing the chip in oc96 mode\n"));

//    status |= cs4224_hard_reset(slice);
//    if(CS_OK != status)
//    {
//      //CS_TRACE(("ERROR trying to reset device\n"));
//        return status;
//    }
 
    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		
//		rules.fracdiv.enable = TRUE;
//		rules.fracdiv.numerator = 0xf66e86;
//    rules.fracdiv.divisor = 0x7;
		rules.fracdiv.enable = FALSE;
		rules.ref_clk_rate                     = CS4224_REF_CLK_155p52;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_2;
		rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_64;
		
		/* Assume an SR transceiver */
    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
		
		rules.enable_fec   = TRUE;
    
//    for(slice = 0; slice < 8; slice=slice+2)
//    {
      rules.polarity_inv = slice_inv[chan*2];  
			status |= cs4224_slice_enter_operational_state(chan*2, &rules);
//    }

    return status;	
}

cs_status Rules_Set_oc48()
{
    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;
//    e_cs4224_polarity_inv_t slice_inv[] = {  \
//        /*0*/ CS4224_POLARITY_INV_DPLX_LINE_TX, \
//        /*1*/ CS4224_POLARITY_INV_NONE, \
//        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_TX , \
//        /*3*/ CS4224_POLARITY_INV_NONE , \
//        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_TX , \
//        /*5*/ CS4224_POLARITY_INV_NONE , \
//        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_TX, \
//        /*7*/ CS4224_POLARITY_INV_NONE , \
//    };
    //CS_PRINTF(("Initializing the chip in oc48 mode\n"));

    status |= cs4224_hard_reset(slice);
    if(CS_OK != status)
    {
      //CS_TRACE(("ERROR trying to reset device\n"));
        return status;
    }
 
    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		
//		rules.fracdiv.enable = TRUE;
//		rules.fracdiv.numerator = 0xf66e86;
//    rules.fracdiv.divisor = 0x7;
//		rules.fracdiv.enable = FALSE;
//		rules.ref_clk_rate                     = CS4224_REF_CLK_155p52;
//		rules.clkdiv.enable = TRUE;
//		rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_4;
//		rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_64;
		//rules.clkdiv.enable = TRUE;
		//rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_4;
		rules.ref_clk_rate                     = (float)f_si570[rate]/rdiv[DIV_Rate[rate].rdivpos]*ddiv[DIV_Rate[rate].ddivpos];//CS4224_REF_CLK_155p52;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = DIV_Rate[rate].ddivpos;//CS4224_CLKDIV_RDIV_BY_64;
    rules.clkdiv.rdiv = DIV_Rate[rate].rdivpos;
		
		/* Assume an SR transceiver */
    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_CX1;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
		
		rules.enable_fec   = TRUE;
    
		for(slice = 2; slice < 6; slice=slice+2)
    {
//      if(polarity_ch[slice/2])  
//			{
//				rules.enable_fec   = TRUE; 
//				rules.polarity_inv = slice_inv[slice];
//        /* Initialize the slice and bring it into operational state */
//        status |=cs4224_slice_enter_operational_state(slice, &rules);
//			}
//			else
				status |= cs4224_slice_enter_operational_state(slice, &rules);
    }
		cs4224_slice_power_down(0);
		cs4224_slice_power_down(1);
		cs4224_slice_power_down(3);
		cs4224_slice_power_down(5);
		cs4224_slice_power_down(6);
		cs4224_slice_power_down(7);

    return status;	
}

cs_status cs4343_init_oc48(int chan)
{
    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;

    //CS_PRINTF(("Initializing the chip in oc48 mode\n"));

//    status |= cs4224_hard_reset(slice);
//    if(CS_OK != status)
//    {
//      //CS_TRACE(("ERROR trying to reset device\n"));
//        return status;
//    }
 
    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		
//		rules.fracdiv.enable = TRUE;
//		rules.fracdiv.numerator = 0xf66e86;
//    rules.fracdiv.divisor = 0x7;
		rules.fracdiv.enable = FALSE;
		rules.ref_clk_rate                     = CS4224_REF_CLK_155p52;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_4;
		rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_64;
		//rules.clkdiv.enable = TRUE;
		//rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_4;
		
		/* Assume an SR transceiver */
    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
//    for(slice = 0; slice < 8; slice=slice+2)
//    {
        status |= cs4224_slice_enter_operational_state(chan*2, &rules);
//    }

    return status;	
}

cs_status cs4343_init_oc48_inv(int chan)
{
    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;
    e_cs4224_polarity_inv_t slice_inv[] = {  \
       /*0*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_HOST_TX) , \
        /*1*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*3*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*5*/ CS4224_POLARITY_INV_NONE , \
        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_RX, \
        /*7*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_RX | CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
    };
    //CS_PRINTF(("Initializing the chip in oc48 mode\n"));

//    status |= cs4224_hard_reset(slice);
//    if(CS_OK != status)
//    {
//      //CS_TRACE(("ERROR trying to reset device\n"));
//        return status;
//    }
 
    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		
//		rules.fracdiv.enable = TRUE;
//		rules.fracdiv.numerator = 0xf66e86;
//    rules.fracdiv.divisor = 0x7;
		rules.fracdiv.enable = FALSE;
		rules.ref_clk_rate                     = CS4224_REF_CLK_155p52;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_4;
		rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_64;
		//rules.clkdiv.enable = TRUE;
		//rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_4;
		
		/* Assume an SR transceiver */
    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
		
		rules.enable_fec   = TRUE;
    
//    for(slice = 0; slice < 8; slice=slice+2)
//    {
      rules.polarity_inv = slice_inv[chan*2];  
			status |= cs4224_slice_enter_operational_state(chan*2, &rules);
//    }

    return status;	
}

cs_status cs4343_init_oc24()
{
    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;

    //CS_PRINTF(("Initializing the chip in oc48 mode\n"));

//    status |= cs4224_hard_reset(slice);
//    if(CS_OK != status)
//    {
//      //CS_TRACE(("ERROR trying to reset device\n"));
//        return status;
//    }
 
    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		
//		rules.fracdiv.enable = TRUE;
//		rules.fracdiv.numerator = 0xf66e86;
//    rules.fracdiv.divisor = 0x7;

		rules.fracdiv.enable = FALSE;
		rules.ref_clk_rate                     = CS4224_REF_CLK_155p52;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_8;
		rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_64;
		
		/* Assume an SR transceiver */
    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
    for(slice = 0; slice < CS4224_MAX_NUM_SLICES(0); slice=slice+2)
    {
        status |= cs4224_slice_enter_operational_state(slice, &rules);
    }

    return status;	
}

cs_status cs4343_init_oc24_inv()
{
    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;
    e_cs4224_polarity_inv_t slice_inv[] = {  \
        /*0*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_HOST_TX) , \
        /*1*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*3*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*5*/ CS4224_POLARITY_INV_NONE , \
        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_RX, \
        /*7*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_RX | CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
    };
    //CS_PRINTF(("Initializing the chip in oc48 mode\n"));

//    status |= cs4224_hard_reset(slice);
//    if(CS_OK != status)
//    {
//      //CS_TRACE(("ERROR trying to reset device\n"));
//        return status;
//    }
 
    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		
//		rules.fracdiv.enable = TRUE;
//		rules.fracdiv.numerator = 0xf66e86;
//    rules.fracdiv.divisor = 0x7;

		rules.fracdiv.enable = FALSE;
		rules.ref_clk_rate                     = CS4224_REF_CLK_155p52;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_8;
		rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_64;
		
		/* Assume an SR transceiver */
    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
		
		rules.enable_fec   = TRUE;
    
    for(slice = 0; slice < CS4224_MAX_NUM_SLICES(0); slice=slice+2)
    {
      rules.polarity_inv = slice_inv[slice];  
			status |= cs4224_slice_enter_operational_state(slice, &rules);
    }

    return status;	
}

cs_status cs4343_init_oc12()
{
    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;

 //CS_PRINTF(("Initializing the chip in oc12 mode\n"));

//    status |= cs4224_hard_reset(slice);
//    if(CS_OK != status)
//    {
//      //CS_TRACE(("ERROR trying to reset device\n"));
//        return status;
//    }
 
    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		
		rules.fracdiv.enable = FALSE;
		rules.ref_clk_rate                     = CS4224_REF_CLK_155p52;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_16;
		rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_64;
		
		/* Assume an SR transceiver */
    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
    for(slice = 0; slice < CS4224_MAX_NUM_SLICES(0); slice=slice+2)
    {
        status |= cs4224_slice_enter_operational_state(slice, &rules);
    }

    return status;
}

cs_status cs4343_init_oc12_inv()
{
    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;
    e_cs4224_polarity_inv_t slice_inv[] = {  \
        /*0*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_HOST_TX) , \
        /*1*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*3*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*5*/ CS4224_POLARITY_INV_NONE , \
        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_RX, \
        /*7*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_RX | CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
    };
 //CS_PRINTF(("Initializing the chip in oc12 mode\n"));

//    status |= cs4224_hard_reset(slice);
//    if(CS_OK != status)
//    {
//      //CS_TRACE(("ERROR trying to reset device\n"));
//        return status;
//    }
 
    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		
		rules.fracdiv.enable = FALSE;
		rules.ref_clk_rate                     = CS4224_REF_CLK_155p52;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_16;
		rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_64;
		
		/* Assume an SR transceiver */
    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
		rules.enable_fec   = TRUE;
		
    for(slice = 0; slice < CS4224_MAX_NUM_SLICES(0); slice=slice+2)
    {
      rules.polarity_inv = slice_inv[slice];  
			status |= cs4224_slice_enter_operational_state(slice, &rules);
    }

    return status;
}

cs_status cs4343_init_fc16g()
{
    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;

 //CS_PRINTF(("Initializing the chip in FC 16G mode\n"));
    
    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. Only the upper 24 bits of the
       slice parameter are used to reference the appropriate ASIC. The
       lower 8 bits are ignored. This method should only be called once
       when the ASIC is first powered on. */
//    status |= cs4224_hard_reset(slice);
//    if(CS_OK != status)
//    {
//      //CS_TRACE(("ERROR trying to reset device\n"));
//        return status;
//    }

    /* Provision all ports of the chip for FC 8G mode. This
     * assumes:
     *   - Device configured for 106.25MHz reference clock
     *   - No fractional divider
     *   - Microcode programmed automatically if not
     *     already programmed via EEPROM.
     * The rules_set_default() method does not actually configure
     * the ASIC, it just defaults the 'rules' structure for
     * the desired application.
     */ 
    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_16G_FC, &rules);

    rules.fracdiv.enable =TRUE;
		rules.fracdiv.numerator =0x800000;
		rules.fracdiv.divisor = 0x10;
		
		/* Assume an SR transceiver */
    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;


    for(slice = 0; slice < CS4224_MAX_NUM_SLICES(0); slice=slice+2)
    {
        status |= cs4224_slice_enter_operational_state(slice, &rules);
    }

    return status;
}


cs_status cs4343_init_fc16g_inv()
{
    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;
    e_cs4224_polarity_inv_t slice_inv[] = {  \
        /*0*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_HOST_TX) , \
        /*1*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*3*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*5*/ CS4224_POLARITY_INV_NONE , \
        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_RX, \
        /*7*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_RX | CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
    };
 //CS_PRINTF(("Initializing the chip in FC 16G mode\n"));
    
    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. Only the upper 24 bits of the
       slice parameter are used to reference the appropriate ASIC. The
       lower 8 bits are ignored. This method should only be called once
       when the ASIC is first powered on. */
//    status |= cs4224_hard_reset(slice);
//    if(CS_OK != status)
//    {
//      //CS_TRACE(("ERROR trying to reset device\n"));
//        return status;
//    }

    /* Provision all ports of the chip for FC 8G mode. This
     * assumes:
     *   - Device configured for 106.25MHz reference clock
     *   - No fractional divider
     *   - Microcode programmed automatically if not
     *     already programmed via EEPROM.
     * The rules_set_default() method does not actually configure
     * the ASIC, it just defaults the 'rules' structure for
     * the desired application.
     */ 
    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_16G_FC, &rules);

    rules.fracdiv.enable =TRUE;
		rules.fracdiv.numerator =0x800000;
		rules.fracdiv.divisor = 0x10;
		
		/* Assume an SR transceiver */
    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
		
		rules.enable_fec   = TRUE;

    for(slice = 0; slice < CS4224_MAX_NUM_SLICES(0); slice=slice+2)
    {
      rules.polarity_inv = slice_inv[slice];  
			status |= cs4224_slice_enter_operational_state(slice, &rules);
    }

    return status;
}

cs_status Rules_Set_fc16g()
{
    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;
//    e_cs4224_polarity_inv_t slice_inv[] = {  \
//        /*0*/ CS4224_POLARITY_INV_DPLX_LINE_TX, \
//        /*1*/ CS4224_POLARITY_INV_NONE, \
//        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_TX , \
//        /*3*/ CS4224_POLARITY_INV_NONE , \
//        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_TX , \
//        /*5*/ CS4224_POLARITY_INV_NONE , \
//        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_TX, \
//        /*7*/ CS4224_POLARITY_INV_NONE , \
//    };
 //CS_PRINTF(("Initializing the chip in FC 10G mode\n"));
    

    status |= cs4224_hard_reset(slice);
    if(CS_OK != status)
    {
      //CS_TRACE(("ERROR trying to reset device\n"));
        return status;
    }


//    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_16G_FC, &rules);

//    rules.fracdiv.enable =TRUE;
//		rules.fracdiv.numerator =0x800000;
//		rules.fracdiv.divisor = 0x10;
//		
//		/* Assume an SR transceiver */
//    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
//    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
//    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
//    
//    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_SR;
//    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
//    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		
//		rules.fracdiv.enable = FALSE;
//		rules.ref_clk_rate                     = 140.25;
//		rules.clkdiv.enable = TRUE;
//		rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_1;
//		rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_100;
		rules.ref_clk_rate                     = (float)f_si570[rate]/rdiv[DIV_Rate[rate].rdivpos]*ddiv[DIV_Rate[rate].ddivpos];//CS4224_REF_CLK_155p52;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = DIV_Rate[rate].ddivpos;//CS4224_CLKDIV_RDIV_BY_64;
    rules.clkdiv.rdiv = DIV_Rate[rate].rdivpos;
		/* Assume an SR transceiver */
    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_CX1;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
		
		rules.enable_fec   = TRUE;

		for(slice = 2; slice < 6; slice=slice+2)
    {
//      if(polarity_ch[slice/2])  
//			{
//				rules.enable_fec   = TRUE; 
//				rules.polarity_inv = slice_inv[slice];
//        /* Initialize the slice and bring it into operational state */
//        status |=cs4224_slice_enter_operational_state(slice, &rules);
//			}
//			else
				status |= cs4224_slice_enter_operational_state(slice, &rules);
    }
		cs4224_slice_power_down(0);
		cs4224_slice_power_down(1);
		cs4224_slice_power_down(3);
		cs4224_slice_power_down(5);
		cs4224_slice_power_down(6);
		cs4224_slice_power_down(7);

    return status;
	
}

cs_status Rules_Set_12g()
{
    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;
//    e_cs4224_polarity_inv_t slice_inv[] = {  \
//        /*0*/ CS4224_POLARITY_INV_DPLX_LINE_TX, \
//        /*1*/ CS4224_POLARITY_INV_NONE, \
//        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_TX , \
//        /*3*/ CS4224_POLARITY_INV_NONE , \
//        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_TX , \
//        /*5*/ CS4224_POLARITY_INV_NONE , \
//        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_TX, \
//        /*7*/ CS4224_POLARITY_INV_NONE , \
//    };
 //CS_PRINTF(("Initializing the chip in FC 10G mode\n"));
    

    status |= cs4224_hard_reset(slice);
    if(CS_OK != status)
    {
      //CS_TRACE(("ERROR trying to reset device\n"));
        return status;
    }


    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
//		rules.ref_clk_rate                     = 156.25;

//		rules.clkdiv.enable = TRUE;
//		rules.clkdiv.fastdiv = 0x3; /* div by 40 (default) */
//		rules.clkdiv.ddiv    = CS4224_DDIV_DIV1;
//		rules.clkdiv.rdiv    = CS4224_RDIV_DIV80;
		rules.ref_clk_rate                     = (float)f_si570[rate]/rdiv[DIV_Rate[rate].rdivpos]*ddiv[DIV_Rate[rate].ddivpos];//CS4224_REF_CLK_155p52;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = DIV_Rate[rate].ddivpos;//CS4224_CLKDIV_RDIV_BY_64;
    rules.clkdiv.rdiv = DIV_Rate[rate].rdivpos;
    /* Assume an SR transceiver */
    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_CX1;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_CX1;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;

		for(slice = 2; slice < 6; slice=slice+2)
    {
//      if(polarity_ch[slice/2])  
//			{
//				rules.enable_fec   = TRUE; 
//				rules.polarity_inv = slice_inv[slice];
//        /* Initialize the slice and bring it into operational state */
//        status |=cs4224_slice_enter_operational_state(slice, &rules);
//			}
//			else
				status |= cs4224_slice_enter_operational_state(slice, &rules);
    }
		cs4224_slice_power_down(0);
		cs4224_slice_power_down(1);
		cs4224_slice_power_down(3);
		cs4224_slice_power_down(5);
		cs4224_slice_power_down(6);
		cs4224_slice_power_down(7);

    return status;
	
}

cs_status Rules_Set_fc10g()
{
    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;
//    e_cs4224_polarity_inv_t slice_inv[] = {  \
//        /*0*/ CS4224_POLARITY_INV_DPLX_LINE_TX, \
//        /*1*/ CS4224_POLARITY_INV_NONE, \
//        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_TX , \
//        /*3*/ CS4224_POLARITY_INV_NONE , \
//        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_TX , \
//        /*5*/ CS4224_POLARITY_INV_NONE , \
//        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_TX, \
//        /*7*/ CS4224_POLARITY_INV_NONE , \
//    };
 //CS_PRINTF(("Initializing the chip in FC 10G mode\n"));
    

    status |= cs4224_hard_reset(slice);
    if(CS_OK != status)
    {
      //CS_TRACE(("ERROR trying to reset device\n"));
        return status;
    }


    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
//		rules.ref_clk_rate                     = 164.35546875;

//		rules.clkdiv.enable = TRUE;
//		rules.clkdiv.fastdiv = 0x3; /* div by 40 (default) */
//		rules.clkdiv.ddiv    = CS4224_DDIV_DIV1;
//		rules.clkdiv.rdiv    = CS4224_RDIV_DIV64;
		rules.ref_clk_rate                     = (float)f_si570[rate]/rdiv[DIV_Rate[rate].rdivpos]*ddiv[DIV_Rate[rate].ddivpos];//CS4224_REF_CLK_155p52;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = DIV_Rate[rate].ddivpos;//CS4224_CLKDIV_RDIV_BY_64;
    rules.clkdiv.rdiv = DIV_Rate[rate].rdivpos;
    /* Assume an SR transceiver */
    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_CX1;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_CX1;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;

		for(slice = 2; slice < 6; slice=slice+2)
    {
//      if(polarity_ch[slice/2])  
//			{
//				rules.enable_fec   = TRUE; 
//				rules.polarity_inv = slice_inv[slice];
//        /* Initialize the slice and bring it into operational state */
//        status |=cs4224_slice_enter_operational_state(slice, &rules);
//			}
//			else
				status |= cs4224_slice_enter_operational_state(slice, &rules);
    }
		cs4224_slice_power_down(0);
		cs4224_slice_power_down(1);
		cs4224_slice_power_down(3);
		cs4224_slice_power_down(5);
		cs4224_slice_power_down(6);
		cs4224_slice_power_down(7);

    return status;
	
}


cs_status cs4343_init_fc10g(int chan)
{
    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;

 //CS_PRINTF(("Initializing the chip in FC 10G mode\n"));
    
    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. Only the upper 24 bits of the
       slice parameter are used to reference the appropriate ASIC. The
       lower 8 bits are ignored. This method should only be called once
       when the ASIC is first powered on. */
//    status |= cs4224_hard_reset(slice);
//    if(CS_OK != status)
//    {
//      //CS_TRACE(("ERROR trying to reset device\n"));
//        return status;
//    }

    /* Provision all ports of the chip for FC 8G mode. This
     * assumes:
     *   - Device configured for 106.25MHz reference clock
     *   - No fractional divider
     *   - Microcode programmed automatically if not
     *     already programmed via EEPROM.
     * The rules_set_default() method does not actually configure
     * the ASIC, it just defaults the 'rules' structure for
     * the desired application.
     */ 
    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);

		rules.ref_clk_rate                     = 164.35546875;

		rules.clkdiv.enable = TRUE;
		rules.clkdiv.fastdiv = 0x3; /* div by 40 (default) */
		rules.clkdiv.ddiv    = CS4224_DDIV_DIV1;
		rules.clkdiv.rdiv    = CS4224_RDIV_DIV64;
    /* Assume an SR transceiver */
    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;


//    for(slice = 0; slice < 8; slice=slice+2)
//    {
        status |= cs4224_slice_enter_operational_state(chan*2, &rules);
//    }

    return status;
}

cs_status cs4343_init_fc10g_inv(int chan)
{
    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;
    e_cs4224_polarity_inv_t slice_inv[] = {  \
        /*0*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_HOST_TX) , \
        /*1*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*3*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*5*/ CS4224_POLARITY_INV_NONE , \
        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_RX, \
        /*7*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_RX | CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
    };
 //CS_PRINTF(("Initializing the chip in FC 10G mode\n"));
    
    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. Only the upper 24 bits of the
       slice parameter are used to reference the appropriate ASIC. The
       lower 8 bits are ignored. This method should only be called once
       when the ASIC is first powered on. */
//    status |= cs4224_hard_reset(slice);
//    if(CS_OK != status)
//    {
//      //CS_TRACE(("ERROR trying to reset device\n"));
//        return status;
//    }

    /* Provision all ports of the chip for FC 8G mode. This
     * assumes:
     *   - Device configured for 106.25MHz reference clock
     *   - No fractional divider
     *   - Microcode programmed automatically if not
     *     already programmed via EEPROM.
     * The rules_set_default() method does not actually configure
     * the ASIC, it just defaults the 'rules' structure for
     * the desired application.
     */ 
    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		rules.ref_clk_rate                     = 164.35546875;

		rules.clkdiv.enable = TRUE;
		rules.clkdiv.fastdiv = 0x3; /* div by 40 (default) */
		rules.clkdiv.ddiv    = CS4224_DDIV_DIV1;
		rules.clkdiv.rdiv    = CS4224_RDIV_DIV64;
    /* Assume an SR transceiver */
    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;

		rules.enable_fec   = TRUE;

//    for(slice = 0; slice < 8; slice=slice+2)
//    {
      rules.polarity_inv = slice_inv[chan*2];  
			status |= cs4224_slice_enter_operational_state(chan*2, &rules);
//    }

    return status;
}

cs_status Rules_Set_fc8g()
{
    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;
//    e_cs4224_polarity_inv_t slice_inv[] = {  \
//        /*0*/ CS4224_POLARITY_INV_DPLX_LINE_TX, \
//        /*1*/ CS4224_POLARITY_INV_NONE, \
//        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_TX , \
//        /*3*/ CS4224_POLARITY_INV_NONE , \
//        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_TX , \
//        /*5*/ CS4224_POLARITY_INV_NONE , \
//        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_TX, \
//        /*7*/ CS4224_POLARITY_INV_NONE , \
//    };
 //CS_PRINTF(("Initializing the chip in FC 10G mode\n"));
    

    status |= cs4224_hard_reset(slice);
    if(CS_OK != status)
    {
      //CS_TRACE(("ERROR trying to reset device\n"));
        return status;
    }


    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		
//		rules.ref_clk_rate                     = 132.8125;//106.25;
//		
//		rules.clkdiv.enable = TRUE;
//		//rules.clkdiv.fastdiv = 0x5; /* div by 66 */
//		rules.clkdiv.ddiv    = CS4224_DDIV_DIV1;
//		rules.clkdiv.rdiv    = CS4224_RDIV_DIV64;
//					  rules->fracdiv.enable                   = TRUE;//10.0
//            rules->fracdiv.divisor                  = 0xA;
//            rules->fracdiv.numerator                = 0x0;
		rules.ref_clk_rate                     = (float)f_si570[rate]/rdiv[DIV_Rate[rate].rdivpos]*ddiv[DIV_Rate[rate].ddivpos];//CS4224_REF_CLK_155p52;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = DIV_Rate[rate].ddivpos;//CS4224_CLKDIV_RDIV_BY_64;
    rules.clkdiv.rdiv = DIV_Rate[rate].rdivpos;
    /* Assume an SR transceiver */
    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_CX1;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_CX1;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;

		for(slice = 2; slice < 6; slice=slice+2)
    {
//      if(polarity_ch[slice/2])  
//			{
//				rules.enable_fec   = TRUE; 
//				rules.polarity_inv = slice_inv[slice];
//        /* Initialize the slice and bring it into operational state */
//        status |=cs4224_slice_enter_operational_state(slice, &rules);
//			}
//			else
				status |= cs4224_slice_enter_operational_state(slice, &rules);
    }
		cs4224_slice_power_down(0);
		cs4224_slice_power_down(1);
		cs4224_slice_power_down(3);
		cs4224_slice_power_down(5);
		cs4224_slice_power_down(6);
		cs4224_slice_power_down(7);

    return status;
	
}

cs_status cs4343_init_fc8g(int chan)
{
    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;

 //CS_PRINTF(("Initializing the chip in FC 8G mode\n"));
    
    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. Only the upper 24 bits of the
       slice parameter are used to reference the appropriate ASIC. The
       lower 8 bits are ignored. This method should only be called once
       when the ASIC is first powered on. */
//    status |= cs4224_hard_reset(slice);
//    if(CS_OK != status)
//    {
//      //CS_TRACE(("ERROR trying to reset device\n"));
//        return status;
//    }

    /* Provision all ports of the chip for FC 8G mode. This
     * assumes:
     *   - Device configured for 106.25MHz reference clock
     *   - No fractional divider
     *   - Microcode programmed automatically if not
     *     already programmed via EEPROM.
     * The rules_set_default() method does not actually configure
     * the ASIC, it just defaults the 'rules' structure for
     * the desired application.
     */ 
    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		
		rules.ref_clk_rate                     = 132.8125;
		
		rules.clkdiv.enable = TRUE;
		//rules.clkdiv.fastdiv = 0x5; /* div by 66 */
		rules.clkdiv.ddiv    = CS4224_DDIV_DIV2;
		rules.clkdiv.rdiv    = CS4224_RDIV_DIV128;
//					  rules->fracdiv.enable                   = TRUE;//10.0
//            rules->fracdiv.divisor                  = 0xA;
//            rules->fracdiv.numerator                = 0x0;

    /* Assume an SR transceiver */
    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;


//    for(slice = 0; slice < 8; slice=slice+2)
//    {
        status |= cs4224_slice_enter_operational_state(chan*2, &rules);
//    }

    return status;
}

cs_status cs4343_init_fc8g_inv(int chan)
{
    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;
    e_cs4224_polarity_inv_t slice_inv[] = {  \
        /*0*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_HOST_TX) , \
        /*1*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*3*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*5*/ CS4224_POLARITY_INV_NONE , \
        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_RX, \
        /*7*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_RX | CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
    };
 //CS_PRINTF(("Initializing the chip in FC 8G mode\n"));
    
    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. Only the upper 24 bits of the
       slice parameter are used to reference the appropriate ASIC. The
       lower 8 bits are ignored. This method should only be called once
       when the ASIC is first powered on. */
//    status |= cs4224_hard_reset(slice);
//    if(CS_OK != status)
//    {
//      //CS_TRACE(("ERROR trying to reset device\n"));
//        return status;
//    }

    /* Provision all ports of the chip for FC 8G mode. This
     * assumes:
     *   - Device configured for 106.25MHz reference clock
     *   - No fractional divider
     *   - Microcode programmed automatically if not
     *     already programmed via EEPROM.
     * The rules_set_default() method does not actually configure
     * the ASIC, it just defaults the 'rules' structure for
     * the desired application.
     */ 
    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		
//		rules.ref_clk_rate                     = CS4224_REF_CLK_106p25;
//		
//		rules.clkdiv.enable = TRUE;
//		//rules.clkdiv.fastdiv = 0x5; /* div by 66 */
//		rules.clkdiv.ddiv    = CS4224_DDIV_DIV1;
//		rules.clkdiv.rdiv    = CS4224_RDIV_DIV80;
//					  rules->fracdiv.enable                   = TRUE;//10.0
//            rules->fracdiv.divisor                  = 0xA;
//            rules->fracdiv.numerator                = 0x0;
		rules.ref_clk_rate                     = 132.8125;
		
		rules.clkdiv.enable = TRUE;
		//rules.clkdiv.fastdiv = 0x5; /* div by 66 */
		rules.clkdiv.ddiv    = CS4224_DDIV_DIV2;
		rules.clkdiv.rdiv    = CS4224_RDIV_DIV128;
    /* Assume an SR transceiver */
    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
		
		rules.enable_fec   = TRUE;

//    for(slice = 0; slice < 8; slice=slice+2)
//    {
      rules.polarity_inv = slice_inv[chan*2];  
			status |= cs4224_slice_enter_operational_state(chan*2, &rules);
//    }

    return status;
}

cs_status Rules_Set_fc4g()
{
    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;
//    e_cs4224_polarity_inv_t slice_inv[] = {  \
//        /*0*/ CS4224_POLARITY_INV_DPLX_LINE_TX, \
//        /*1*/ CS4224_POLARITY_INV_NONE, \
//        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_TX , \
//        /*3*/ CS4224_POLARITY_INV_NONE , \
//        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_TX , \
//        /*5*/ CS4224_POLARITY_INV_NONE , \
//        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_TX, \
//        /*7*/ CS4224_POLARITY_INV_NONE , \
//    };
 //CS_PRINTF(("Initializing the chip in FC 10G mode\n"));
    

    status |= cs4224_hard_reset(slice);
    if(CS_OK != status)
    {
      //CS_TRACE(("ERROR trying to reset device\n"));
        return status;
    }


    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
//		rules.ref_clk_rate                     = 106.25;
//		
//		rules.clkdiv.enable = TRUE;
//		//rules.clkdiv.fastdiv = 0x5; /* div by 66 */
//		rules.clkdiv.ddiv    = CS4224_DDIV_DIV2;
//		rules.clkdiv.rdiv    = CS4224_RDIV_DIV80;
				rules.ref_clk_rate                     = (float)f_si570[rate]/rdiv[DIV_Rate[rate].rdivpos]*ddiv[DIV_Rate[rate].ddivpos];//CS4224_REF_CLK_155p52;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = DIV_Rate[rate].ddivpos;//CS4224_CLKDIV_RDIV_BY_64;
    rules.clkdiv.rdiv = DIV_Rate[rate].rdivpos;
    /* Assume an SR transceiver */
    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_CX1;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;

		for(slice = 2; slice < 6; slice=slice+2)
    {
//      if(polarity_ch[slice/2])  
//			{
//				rules.enable_fec   = TRUE; 
//				rules.polarity_inv = slice_inv[slice];
//        /* Initialize the slice and bring it into operational state */
//        status |=cs4224_slice_enter_operational_state(slice, &rules);
//			}
//			else
				status |= cs4224_slice_enter_operational_state(slice, &rules);
    }
		cs4224_slice_power_down(0);
		cs4224_slice_power_down(1);
		cs4224_slice_power_down(3);
		cs4224_slice_power_down(5);
		cs4224_slice_power_down(6);
		cs4224_slice_power_down(7);

    return status;
	
}


cs_status cs4343_init_fc4g(int chan)
{
    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;

 //CS_PRINTF(("Initializing the chip in FC 4G mode\n"));
    
    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. Only the upper 24 bits of the
       slice parameter are used to reference the appropriate ASIC. The
       lower 8 bits are ignored. This method should only be called once
       when the ASIC is first powered on. */
//    status |= cs4224_hard_reset(slice);
//    if(CS_OK != status)
//    {
//      //CS_TRACE(("ERROR trying to reset device\n"));
//        return status;
//    }

    /* Provision all ports of the chip for FC 8G mode. This
     * assumes:
     *   - Device configured for 106.25MHz reference clock
     *   - No fractional divider
     *   - Microcode programmed automatically if not
     *     already programmed via EEPROM.
     * The rules_set_default() method does not actually configure
     * the ASIC, it just defaults the 'rules' structure for
     * the desired application.
     */ 
    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		rules.ref_clk_rate                     = 132.8125;
		
		rules.clkdiv.enable = TRUE;
		//rules.clkdiv.fastdiv = 0x5; /* div by 66 */
		rules.clkdiv.ddiv    = CS4224_DDIV_DIV4;
		rules.clkdiv.rdiv    = CS4224_RDIV_DIV128;
    /* Assume an SR transceiver */
    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;


//    for(slice = 0; slice < 8; slice=slice+2)
//    {
        status |= cs4224_slice_enter_operational_state(chan*2, &rules);
//    }

    return status;
}

cs_status cs4343_init_fc4g_inv(int chan )
{
    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;
    e_cs4224_polarity_inv_t slice_inv[] = {  \
        /*0*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_HOST_TX) , \
        /*1*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*3*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*5*/ CS4224_POLARITY_INV_NONE , \
        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_RX, \
        /*7*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_RX | CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
    };
 //CS_PRINTF(("Initializing the chip in FC 4G mode\n"));
    
    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. Only the upper 24 bits of the
       slice parameter are used to reference the appropriate ASIC. The
       lower 8 bits are ignored. This method should only be called once
       when the ASIC is first powered on. */
//    status |= cs4224_hard_reset(slice);
//    if(CS_OK != status)
//    {
//      //CS_TRACE(("ERROR trying to reset device\n"));
//        return status;
//    }

    /* Provision all ports of the chip for FC 8G mode. This
     * assumes:
     *   - Device configured for 106.25MHz reference clock
     *   - No fractional divider
     *   - Microcode programmed automatically if not
     *     already programmed via EEPROM.
     * The rules_set_default() method does not actually configure
     * the ASIC, it just defaults the 'rules' structure for
     * the desired application.
     */ 
    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
//		rules.ref_clk_rate                     = CS4224_REF_CLK_106p25;
//		
//		rules.clkdiv.enable = TRUE;
//		//rules.clkdiv.fastdiv = 0x5; /* div by 66 */
//		rules.clkdiv.ddiv    = CS4224_DDIV_DIV2;
//		rules.clkdiv.rdiv    = CS4224_RDIV_DIV80;
		rules.ref_clk_rate                     = 132.8125;
		
		rules.clkdiv.enable = TRUE;
		//rules.clkdiv.fastdiv = 0x5; /* div by 66 */
		rules.clkdiv.ddiv    = CS4224_DDIV_DIV4;
		rules.clkdiv.rdiv    = CS4224_RDIV_DIV128;
    /* Assume an SR transceiver */
    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;

		rules.enable_fec   = TRUE;

//    for(slice = 0; slice < 8; slice=slice+2)
//    {
      rules.polarity_inv = slice_inv[chan*2];  
			status |= cs4224_slice_enter_operational_state(chan*2, &rules);
//    }

    return status;
}

cs_status Rules_Set_fc2g()
{
    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;
//    e_cs4224_polarity_inv_t slice_inv[] = {  \
//        /*0*/ CS4224_POLARITY_INV_DPLX_LINE_TX, \
//        /*1*/ CS4224_POLARITY_INV_NONE, \
//        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_TX , \
//        /*3*/ CS4224_POLARITY_INV_NONE , \
//        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_TX , \
//        /*5*/ CS4224_POLARITY_INV_NONE , \
//        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_TX, \
//        /*7*/ CS4224_POLARITY_INV_NONE , \
//    };
 //CS_PRINTF(("Initializing the chip in FC 10G mode\n"));
    

    status |= cs4224_hard_reset(slice);
    if(CS_OK != status)
    {
      //CS_TRACE(("ERROR trying to reset device\n"));
        return status;
    }


    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
//		rules.ref_clk_rate                     = 132.8125;//106.25;
//		
//		rules.clkdiv.enable = TRUE;
//		//rules.clkdiv.fastdiv = 0x5; /* div by 66 */
//		rules.clkdiv.ddiv    = CS4224_DDIV_DIV8;
//		rules.clkdiv.rdiv    = CS4224_RDIV_DIV128;
		rules.ref_clk_rate                     = (float)f_si570[rate]/rdiv[DIV_Rate[rate].rdivpos]*ddiv[DIV_Rate[rate].ddivpos];//CS4224_REF_CLK_155p52;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = DIV_Rate[rate].ddivpos;//CS4224_CLKDIV_RDIV_BY_64;
    rules.clkdiv.rdiv = DIV_Rate[rate].rdivpos;
    /* Assume an SR transceiver */
    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_CX1;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;

		for(slice = 2; slice < 6; slice=slice+2)
    {
//      if(polarity_ch[slice/2])  
//			{
//				rules.enable_fec   = TRUE; 
//				rules.polarity_inv = slice_inv[slice];
//        /* Initialize the slice and bring it into operational state */
//        status |=cs4224_slice_enter_operational_state(slice, &rules);
//			}
//			else
				status |= cs4224_slice_enter_operational_state(slice, &rules);
    }
		cs4224_slice_power_down(0);
		cs4224_slice_power_down(1);
		cs4224_slice_power_down(3);
		cs4224_slice_power_down(5);
		cs4224_slice_power_down(6);
		cs4224_slice_power_down(7);

    return status;
	
}

cs_status cs4343_init_fc2g(int chan)
{
    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;

 //CS_PRINTF(("Initializing the chip in FC 2G mode\n"));
    
    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. Only the upper 24 bits of the
       slice parameter are used to reference the appropriate ASIC. The
       lower 8 bits are ignored. This method should only be called once
       when the ASIC is first powered on. */
//    status |= cs4224_hard_reset(slice);
//    if(CS_OK != status)
//    {
//      //CS_TRACE(("ERROR trying to reset device\n"));
//        return status;
//    }

    /* Provision all ports of the chip for FC 8G mode. This
     * assumes:
     *   - Device configured for 106.25MHz reference clock
     *   - No fractional divider
     *   - Microcode programmed automatically if not
     *     already programmed via EEPROM.
     * The rules_set_default() method does not actually configure
     * the ASIC, it just defaults the 'rules' structure for
     * the desired application.
     */ 
    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		rules.ref_clk_rate                     = 132.8125;
		
		rules.clkdiv.enable = TRUE;
		//rules.clkdiv.fastdiv = 0x5; /* div by 66 */
		rules.clkdiv.ddiv    = CS4224_DDIV_DIV8;
		rules.clkdiv.rdiv    = CS4224_RDIV_DIV128;
    /* Assume an SR transceiver */
    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;


//    for(slice = 0; slice < 8; slice=slice+2)
//    {
        status |= cs4224_slice_enter_operational_state(chan*2, &rules);
//    }

    return status;
}

cs_status cs4343_init_fc2g_inv(int chan)
{
    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;
    e_cs4224_polarity_inv_t slice_inv[] = {  \
        /*0*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_HOST_TX) , \
        /*1*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*3*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*5*/ CS4224_POLARITY_INV_NONE , \
        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_RX, \
        /*7*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_RX | CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
    };
 //CS_PRINTF(("Initializing the chip in FC 2G mode\n"));
    
    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. Only the upper 24 bits of the
       slice parameter are used to reference the appropriate ASIC. The
       lower 8 bits are ignored. This method should only be called once
       when the ASIC is first powered on. */
//    status |= cs4224_hard_reset(slice);
//    if(CS_OK != status)
//    {
//      //CS_TRACE(("ERROR trying to reset device\n"));
//        return status;
//    }

    /* Provision all ports of the chip for FC 8G mode. This
     * assumes:
     *   - Device configured for 106.25MHz reference clock
     *   - No fractional divider
     *   - Microcode programmed automatically if not
     *     already programmed via EEPROM.
     * The rules_set_default() method does not actually configure
     * the ASIC, it just defaults the 'rules' structure for
     * the desired application.
     */ 
    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
//		rules.ref_clk_rate                     = CS4224_REF_CLK_106p25;
//		
//		rules.clkdiv.enable = TRUE;
//		//rules.clkdiv.fastdiv = 0x5; /* div by 66 */
//		rules.clkdiv.ddiv    = CS4224_DDIV_DIV4;
//		rules.clkdiv.rdiv    = CS4224_RDIV_DIV80;
		rules.ref_clk_rate                     = 132.8125;
		
		rules.clkdiv.enable = TRUE;
		//rules.clkdiv.fastdiv = 0x5; /* div by 66 */
		rules.clkdiv.ddiv    = CS4224_DDIV_DIV8;
		rules.clkdiv.rdiv    = CS4224_RDIV_DIV128;
    /* Assume an SR transceiver */
    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
		
		rules.enable_fec   = TRUE;

//    for(slice = 0; slice < 8; slice=slice+2)
//    {
      rules.polarity_inv = slice_inv[chan*2];  
			status |= cs4224_slice_enter_operational_state(chan*2, &rules);
//    }

    return status;
}

cs_status cs4343_init_fc1g()
{
    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;

 //CS_PRINTF(("Initializing the chip in FC 1G mode\n"));
    
    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. Only the upper 24 bits of the
       slice parameter are used to reference the appropriate ASIC. The
       lower 8 bits are ignored. This method should only be called once
       when the ASIC is first powered on. */
//    status |= cs4224_hard_reset(slice);
//    if(CS_OK != status)
//    {
//      //CS_TRACE(("ERROR trying to reset device\n"));
//        return status;
//    }

    /* Provision all ports of the chip for FC 8G mode. This
     * assumes:
     *   - Device configured for 106.25MHz reference clock
     *   - No fractional divider
     *   - Microcode programmed automatically if not
     *     already programmed via EEPROM.
     * The rules_set_default() method does not actually configure
     * the ASIC, it just defaults the 'rules' structure for
     * the desired application.
     */ 
    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		rules.ref_clk_rate                     = CS4224_REF_CLK_106p25;
		
		rules.clkdiv.enable = TRUE;
		//rules.clkdiv.fastdiv = 0x5; /* div by 66 */
		rules.clkdiv.ddiv    = CS4224_DDIV_DIV8;
		rules.clkdiv.rdiv    = CS4224_RDIV_DIV80;
    /* Assume an SR transceiver */
    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;


    for(slice = 0; slice < CS4224_MAX_NUM_SLICES(0); slice=slice+2)
    {
        status |= cs4224_slice_enter_operational_state(slice, &rules);
    }

    return status;
}

cs_status cs4343_init_fc1g_inv()
{
    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;
    e_cs4224_polarity_inv_t slice_inv[] = {  \
        /*0*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_HOST_TX) , \
        /*1*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*3*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*5*/ CS4224_POLARITY_INV_NONE , \
        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_RX, \
        /*7*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_RX | CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
    };
 //CS_PRINTF(("Initializing the chip in FC 1G mode\n"));
    
    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. Only the upper 24 bits of the
       slice parameter are used to reference the appropriate ASIC. The
       lower 8 bits are ignored. This method should only be called once
       when the ASIC is first powered on. */
//    status |= cs4224_hard_reset(slice);
//    if(CS_OK != status)
//    {
//      //CS_TRACE(("ERROR trying to reset device\n"));
//        return status;
//    }

    /* Provision all ports of the chip for FC 8G mode. This
     * assumes:
     *   - Device configured for 106.25MHz reference clock
     *   - No fractional divider
     *   - Microcode programmed automatically if not
     *     already programmed via EEPROM.
     * The rules_set_default() method does not actually configure
     * the ASIC, it just defaults the 'rules' structure for
     * the desired application.
     */ 
    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		rules.ref_clk_rate                     = CS4224_REF_CLK_106p25;
		
		rules.clkdiv.enable = TRUE;
		//rules.clkdiv.fastdiv = 0x5; /* div by 66 */
		rules.clkdiv.ddiv    = CS4224_DDIV_DIV8;
		rules.clkdiv.rdiv    = CS4224_RDIV_DIV80;
    /* Assume an SR transceiver */
    rules.rx_if.dplx_line_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    
    rules.rx_if.dplx_host_edc_mode         = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
		
		rules.enable_fec   = TRUE;


    for(slice = 0; slice < CS4224_MAX_NUM_SLICES(0); slice=slice+2)
    {
      rules.polarity_inv = slice_inv[slice];  
			status |= cs4224_slice_enter_operational_state(slice, &rules);
    }

    return status;
}

cs_status Rules_Set_10ginfi()
{
    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;
//    e_cs4224_polarity_inv_t slice_inv[] = {  \
//        /*0*/ CS4224_POLARITY_INV_DPLX_LINE_TX, \
//        /*1*/ CS4224_POLARITY_INV_NONE, \
//        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_TX , \
//        /*3*/ CS4224_POLARITY_INV_NONE , \
//        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_TX , \
//        /*5*/ CS4224_POLARITY_INV_NONE , \
//        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_TX, \
//        /*7*/ CS4224_POLARITY_INV_NONE , \
//    };
 //CS_PRINTF(("Initializing the chip in FC 10G mode\n"));
    

    status |= cs4224_hard_reset(slice);
    if(CS_OK != status)
    {
      //CS_TRACE(("ERROR trying to reset device\n"));
        return status;
    }

    cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);

//    /* Configure the RDIV/DDIV parameters */
//    rules.clkdiv.enable = TRUE;
//    rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_64;
		rules.ref_clk_rate                     = (float)f_si570[rate]/rdiv[DIV_Rate[rate].rdivpos]*ddiv[DIV_Rate[rate].ddivpos];//CS4224_REF_CLK_155p52;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = DIV_Rate[rate].ddivpos;//CS4224_CLKDIV_RDIV_BY_64;
    rules.clkdiv.rdiv = DIV_Rate[rate].rdivpos;
    /* Configuring the microcode image */
    rules.rx_if.dplx_line_edc_mode = CS_HSIO_EDC_MODE_CX1;
    rules.rx_if.dplx_host_edc_mode = CS_HSIO_EDC_MODE_CX1;

    /* Configuring the trace loss settings */
    /* The driver trace loss may need further tuning for the target application */
    /* The equalizer trace loss is currently only used in SR mode or when the */
    /* microcode is disabled */
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;

		for(slice = 2; slice < 6; slice=slice+2)
    {
//      if(polarity_ch[slice/2])  
//			{
//				rules.enable_fec   = TRUE; 
//				rules.polarity_inv = slice_inv[slice];
//        /* Initialize the slice and bring it into operational state */
//        status |=cs4224_slice_enter_operational_state(slice, &rules);
//			}
//			else
				status |= cs4224_slice_enter_operational_state(slice, &rules);
    }
		cs4224_slice_power_down(0);
		cs4224_slice_power_down(1);
		cs4224_slice_power_down(3);
		cs4224_slice_power_down(5);
		cs4224_slice_power_down(6);
		cs4224_slice_power_down(7);

    return status;	
}

cs_status cs4343_configure_10g_infiniband(int chan)
{
    cs_status status = CS_OK; /* return code, CS_OK == good
                                              CS_ERROR == bad*/
    cs_uint32 slice = 0;      /* The slice/channel being configured*/
    cs4224_rules_t rules;     /* The configuration rules for initializing
                                 the device */


    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. The slice parameter is only
       passed so that the upper 24 bits can be used to reference the
       appropriate ASIC. The hard reset is necessary to properly
       initialize all registers since some are not clocked by default
       when the external reset pin is toggled. Do not call this method
       multiple times each time you configure a slice as it will reset
       the whole ASIC each time. It should only be done once on power up.*/
//    cs4224_hard_reset(slice);

    /* The cs4224_rules_set_default() method is used to default the rules */
    /* for a particular target application. They can be customized before */
    /* calling cs4224_slice_enter_operational_state() */
    /* Data rate = 10000.00000 MHz */
    /* Clock rate = 156.25 MHz */
    cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);

    /* Configure the RDIV/DDIV parameters */
    rules.clkdiv.enable = TRUE;
    rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_64;

    /* Configuring the microcode image */
    rules.rx_if.dplx_line_edc_mode = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_edc_mode = CS_HSIO_EDC_MODE_SR;

    /* Configuring the trace loss settings */
    /* The driver trace loss may need further tuning for the target application */
    /* The equalizer trace loss is currently only used in SR mode or when the */
    /* microcode is disabled */
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;


//    for(slice = 0; slice < 8; slice=slice+2)
//    {
        status |= cs4224_slice_enter_operational_state(chan*2, &rules);

//    }

    return status;
}

cs_status cs4343_configure_10g_infiniband_inv(int chan)
{
    cs_status status = CS_OK; /* return code, CS_OK == good
                                              CS_ERROR == bad*/
    cs_uint32 slice = 0;      /* The slice/channel being configured*/
    cs4224_rules_t rules;     /* The configuration rules for initializing
                                 the device */
    e_cs4224_polarity_inv_t slice_inv[] = {  \
        /*0*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_HOST_TX) , \
        /*1*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*3*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*5*/ CS4224_POLARITY_INV_NONE , \
        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_RX, \
        /*7*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_RX | CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
    };

    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. The slice parameter is only
       passed so that the upper 24 bits can be used to reference the
       appropriate ASIC. The hard reset is necessary to properly
       initialize all registers since some are not clocked by default
       when the external reset pin is toggled. Do not call this method
       multiple times each time you configure a slice as it will reset
       the whole ASIC each time. It should only be done once on power up.*/
//    cs4224_hard_reset(slice);

    /* The cs4224_rules_set_default() method is used to default the rules */
    /* for a particular target application. They can be customized before */
    /* calling cs4224_slice_enter_operational_state() */
    /* Data rate = 10000.00000 MHz */
    /* Clock rate = 156.25 MHz */
    cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);

    /* Configure the RDIV/DDIV parameters */
    rules.clkdiv.enable = TRUE;
    rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_64;

    /* Configuring the microcode image */
    rules.rx_if.dplx_line_edc_mode = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_edc_mode = CS_HSIO_EDC_MODE_SR;

    /* Configuring the trace loss settings */
    /* The driver trace loss may need further tuning for the target application */
    /* The equalizer trace loss is currently only used in SR mode or when the */
    /* microcode is disabled */
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
		
		rules.enable_fec   = TRUE;

//    for(slice = 0; slice < 8; slice=slice+2)
//    {
      rules.polarity_inv = slice_inv[chan*2];  
			status |= cs4224_slice_enter_operational_state(chan*2, &rules);
//    }
    return status;
}


cs_status Rules_Set_5ginfi()
{
    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;
//    e_cs4224_polarity_inv_t slice_inv[] = {  \
//        /*0*/ CS4224_POLARITY_INV_DPLX_LINE_TX, \
//        /*1*/ CS4224_POLARITY_INV_NONE, \
//        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_TX , \
//        /*3*/ CS4224_POLARITY_INV_NONE , \
//        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_TX , \
//        /*5*/ CS4224_POLARITY_INV_NONE , \
//        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_TX, \
//        /*7*/ CS4224_POLARITY_INV_NONE , \
//    };
 //CS_PRINTF(("Initializing the chip in FC 10G mode\n"));
    

    status |= cs4224_hard_reset(slice);
    if(CS_OK != status)
    {
      //CS_TRACE(("ERROR trying to reset device\n"));
        return status;
    }

    cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);

//    /* Configure the RDIV/DDIV parameters */
//    rules.clkdiv.enable = TRUE;
//    rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_2;
//    rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_64;
				rules.ref_clk_rate                     = (float)f_si570[rate]/rdiv[DIV_Rate[rate].rdivpos]*ddiv[DIV_Rate[rate].ddivpos];//CS4224_REF_CLK_155p52;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = DIV_Rate[rate].ddivpos;//CS4224_CLKDIV_RDIV_BY_64;
    rules.clkdiv.rdiv = DIV_Rate[rate].rdivpos;

    /* Configuring the microcode image */
//    rules.rx_if.dplx_line_edc_mode = CS_HSIO_EDC_MODE_5G_BP;
//    rules.rx_if.dplx_host_edc_mode = CS_HSIO_EDC_MODE_5G_BP;
    rules.rx_if.dplx_line_edc_mode = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_edc_mode = CS_HSIO_EDC_MODE_CX1;
    /* Configuring the trace loss settings */
    /* The driver trace loss may need further tuning for the target application */
    /* The equalizer trace loss is currently only used in SR mode or when the */
    /* microcode is disabled */
//    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
//    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;

		for(slice = 2; slice < 6; slice=slice+2)
    {
//      if(polarity_ch[slice/2])  
//			{
//				rules.enable_fec   = TRUE; 
//				rules.polarity_inv = slice_inv[slice];
//        /* Initialize the slice and bring it into operational state */
//        status |=cs4224_slice_enter_operational_state(slice, &rules);
//			}
//			else
				status |= cs4224_slice_enter_operational_state(slice, &rules);
    }
		cs4224_slice_power_down(0);
		cs4224_slice_power_down(1);
		cs4224_slice_power_down(3);
		cs4224_slice_power_down(5);
		cs4224_slice_power_down(6);
		cs4224_slice_power_down(7);

    return status;	
}


cs_status cs4343_configure_5g_infiniband(int chan)
{
    cs_status status = CS_OK; /* return code, CS_OK == good
                                              CS_ERROR == bad*/
    cs_uint32 slice = 0;      /* The slice/channel being configured*/
    cs4224_rules_t rules;     /* The configuration rules for initializing
                                 the device */


    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. The slice parameter is only
       passed so that the upper 24 bits can be used to reference the
       appropriate ASIC. The hard reset is necessary to properly
       initialize all registers since some are not clocked by default
       when the external reset pin is toggled. Do not call this method
       multiple times each time you configure a slice as it will reset
       the whole ASIC each time. It should only be done once on power up.*/
//    cs4224_hard_reset(slice);

    /* The cs4224_rules_set_default() method is used to default the rules */
    /* for a particular target application. They can be customized before */
    /* calling cs4224_slice_enter_operational_state() */
    /* Data rate = 5000.00000 MHz */
    /* Clock rate = 156.25 MHz */
    cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);

    /* Configure the RDIV/DDIV parameters */
    rules.clkdiv.enable = TRUE;
    rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_2;
    rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_64;

    /* Configuring the microcode image */
//    rules.rx_if.dplx_line_edc_mode = CS_HSIO_EDC_MODE_5G_BP;
//    rules.rx_if.dplx_host_edc_mode = CS_HSIO_EDC_MODE_5G_BP;
    rules.rx_if.dplx_line_edc_mode = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_edc_mode = CS_HSIO_EDC_MODE_SR;
    /* Configuring the trace loss settings */
    /* The driver trace loss may need further tuning for the target application */
    /* The equalizer trace loss is currently only used in SR mode or when the */
    /* microcode is disabled */
//    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
//    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;

//    for(slice = 0; slice < 8; slice=slice+2)
//    {
        status |= cs4224_slice_enter_operational_state(chan*2, &rules);

//    }

    return status;
}

cs_status cs4343_configure_5g_infiniband_inv(int chan)
{
    cs_status status = CS_OK; /* return code, CS_OK == good
                                              CS_ERROR == bad*/
    cs_uint32 slice = 0;      /* The slice/channel being configured*/
    cs4224_rules_t rules;     /* The configuration rules for initializing
                                 the device */
    e_cs4224_polarity_inv_t slice_inv[] = {  \
        /*0*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_HOST_TX) , \
        /*1*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*3*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*5*/ CS4224_POLARITY_INV_NONE , \
        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_RX, \
        /*7*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_RX | CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
    };

    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. The slice parameter is only
       passed so that the upper 24 bits can be used to reference the
       appropriate ASIC. The hard reset is necessary to properly
       initialize all registers since some are not clocked by default
       when the external reset pin is toggled. Do not call this method
       multiple times each time you configure a slice as it will reset
       the whole ASIC each time. It should only be done once on power up.*/
//    cs4224_hard_reset(slice);

    /* The cs4224_rules_set_default() method is used to default the rules */
    /* for a particular target application. They can be customized before */
    /* calling cs4224_slice_enter_operational_state() */
    /* Data rate = 5000.00000 MHz */
    /* Clock rate = 156.25 MHz */
    cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);

    /* Configure the RDIV/DDIV parameters */
    rules.clkdiv.enable = TRUE;
    rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_2;
    rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_64;

//    /* Configuring the microcode image */
//    rules.rx_if.dplx_line_edc_mode = CS_HSIO_EDC_MODE_5G_BP;
//    rules.rx_if.dplx_host_edc_mode = CS_HSIO_EDC_MODE_5G_BP;
    rules.rx_if.dplx_line_edc_mode = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_edc_mode = CS_HSIO_EDC_MODE_SR;
    /* Configuring the trace loss settings */
    /* The driver trace loss may need further tuning for the target application */
    /* The equalizer trace loss is currently only used in SR mode or when the */
    /* microcode is disabled */
//    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
//    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;		
		rules.enable_fec   = TRUE;

//    for(slice = 0; slice < 8; slice=slice+2)
//    {
      rules.polarity_inv = slice_inv[chan*2];  
			status |= cs4224_slice_enter_operational_state(chan*2, &rules);
//    }

    return status;
}

cs_status cs4343_configure_odu2()
{
    cs_status status = CS_OK; /* return code, CS_OK == good
                                              CS_ERROR == bad*/
    cs_uint32 slice = 0;      /* The slice/channel being configured*/
    cs4224_rules_t rules;     /* The configuration rules for initializing
                                 the device */


    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. The slice parameter is only
       passed so that the upper 24 bits can be used to reference the
       appropriate ASIC. The hard reset is necessary to properly
       initialize all registers since some are not clocked by default
       when the external reset pin is toggled. Do not call this method
       multiple times each time you configure a slice as it will reset
       the whole ASIC each time. It should only be done once on power up.*/
//    cs4224_hard_reset(slice);

    /* The cs4224_rules_set_default() method is used to default the rules */
    /* for a particular target application. They can be customized before */
    /* calling cs4224_slice_enter_operational_state() */
    /* Data rate = 10037.27000 MHz */
    /* Clock rate = 156.25 MHz */
    cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);

		rules.ref_clk_rate                     = 100.3727;//CS4224_REF_CLK_106p25;
		rules.fracdiv.enable                   = FALSE; 
	  rules.clkdiv.enable = TRUE;
		rules.clkdiv.fastdiv = 0x5; /* div by 66 */ 
		rules.clkdiv.ddiv    = CS4224_DDIV_DIV1;
		rules.clkdiv.rdiv    = CS4224_RDIV_DIV100;  
    /* Enable the fractional divider since 10037.27000/64 != 156.25 */
    /*   divisor   = floor(vco_rate/(8.0*ref_clk)) */
    /*             = floor(10037.27000/(8.0*156.25) */
    /*   numerator = round(pow(2.0,24)*((vco_rate/(8*ref_clk))-divisor,6) */
    /*             = round(16777216.000000 * (8.029816 - 8),6) */
    /*             = round(500229.472256,6) */
    /* Double checking the calculation: */
    /*             = 8 * ref_clk * (divisor + (numerator/0xffffff)) */
    /*             = 8 * 156.250000 * (divisor + (0.029816) */
    /*             = 8 * 156.250000 * (8.029816) */
    /*             = 10037.269967 */
//    rules.fracdiv.enable    = TRUE;
//    rules.fracdiv.numerator = 0x7a205;
//    rules.fracdiv.divisor   = 0x8;

    /* Configuring the microcode image */
    rules.rx_if.dplx_line_edc_mode = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_edc_mode = CS_HSIO_EDC_MODE_SR;

    /* Configuring the trace loss settings */
    /* The driver trace loss may need further tuning for the target application */
    /* The equalizer trace loss is currently only used in SR mode or when the */
    /* microcode is disabled */
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;


    for(slice = 0; slice < CS4224_MAX_NUM_SLICES(0); slice=slice+2)
    {
        status |= cs4224_slice_enter_operational_state(slice, &rules);

    }

    return status;
}

cs_status cs4343_configure_odu2_inv()
{
    cs_status status = CS_OK; /* return code, CS_OK == good
                                              CS_ERROR == bad*/
    cs_uint32 slice = 0;      /* The slice/channel being configured*/
    cs4224_rules_t rules;     /* The configuration rules for initializing
                                 the device */
    e_cs4224_polarity_inv_t slice_inv[] = {  \
        /*0*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_HOST_TX) , \
        /*1*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*3*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*5*/ CS4224_POLARITY_INV_NONE , \
        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_RX, \
        /*7*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_RX | CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
    };

    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. The slice parameter is only
       passed so that the upper 24 bits can be used to reference the
       appropriate ASIC. The hard reset is necessary to properly
       initialize all registers since some are not clocked by default
       when the external reset pin is toggled. Do not call this method
       multiple times each time you configure a slice as it will reset
       the whole ASIC each time. It should only be done once on power up.*/
//    cs4224_hard_reset(slice);

    /* The cs4224_rules_set_default() method is used to default the rules */
    /* for a particular target application. They can be customized before */
    /* calling cs4224_slice_enter_operational_state() */
    /* Data rate = 10037.27000 MHz */
    /* Clock rate = 156.25 MHz */
    cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);

		rules.ref_clk_rate                     = 100.3727;//CS4224_REF_CLK_106p25;
		rules.fracdiv.enable                   = FALSE; 
	  rules.clkdiv.enable = TRUE;
		rules.clkdiv.fastdiv = 0x5; /* div by 66 */ 
		rules.clkdiv.ddiv    = CS4224_DDIV_DIV1;
		rules.clkdiv.rdiv    = CS4224_RDIV_DIV100;  
    /* Enable the fractional divider since 10037.27000/64 != 156.25 */
    /*   divisor   = floor(vco_rate/(8.0*ref_clk)) */
    /*             = floor(10037.27000/(8.0*156.25) */
    /*   numerator = round(pow(2.0,24)*((vco_rate/(8*ref_clk))-divisor,6) */
    /*             = round(16777216.000000 * (8.029816 - 8),6) */
    /*             = round(500229.472256,6) */
    /* Double checking the calculation: */
    /*             = 8 * ref_clk * (divisor + (numerator/0xffffff)) */
    /*             = 8 * 156.250000 * (divisor + (0.029816) */
    /*             = 8 * 156.250000 * (8.029816) */
    /*             = 10037.269967 */
//    rules.fracdiv.enable    = TRUE;
//    rules.fracdiv.numerator = 0x7a205;
//    rules.fracdiv.divisor   = 0x8;

    /* Configuring the microcode image */
    rules.rx_if.dplx_line_edc_mode = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_edc_mode = CS_HSIO_EDC_MODE_SR;

    /* Configuring the trace loss settings */
    /* The driver trace loss may need further tuning for the target application */
    /* The equalizer trace loss is currently only used in SR mode or when the */
    /* microcode is disabled */
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
		
		rules.enable_fec   = TRUE;


    for(slice = 0; slice < CS4224_MAX_NUM_SLICES(0); slice=slice+2)
    {
      rules.polarity_inv = slice_inv[slice];  
			status |= cs4224_slice_enter_operational_state(slice, &rules);
    }
    return status;
}

/* Configuration script for OTU1     (2666.057Mbps)     traffic */
cs_status cs4343_configure_otu1()
{
    cs_status status = CS_OK; /* return code, CS_OK == good
                                              CS_ERROR == bad*/
    cs_uint32 slice = 0;      /* The slice/channel being configured*/
    cs4224_rules_t rules;     /* The configuration rules for initializing
                                 the device */


    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. The slice parameter is only
       passed so that the upper 24 bits can be used to reference the
       appropriate ASIC. The hard reset is necessary to properly
       initialize all registers since some are not clocked by default
       when the external reset pin is toggled. Do not call this method
       multiple times each time you configure a slice as it will reset
       the whole ASIC each time. It should only be done once on power up.*/
//    cs4224_hard_reset(slice);

    /* The cs4224_rules_set_default() method is used to default the rules */
    /* for a particular target application. They can be customized before */
    /* calling cs4224_slice_enter_operational_state() */
    /* Data rate = 2666.05700 MHz */
    /* Clock rate = 156.25 MHz */
    cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		rules.ref_clk_rate                     = 166.6285625;//CS4224_REF_CLK_106p25;
		rules.fracdiv.enable                   = FALSE; 
	  rules.clkdiv.enable = TRUE;
		rules.clkdiv.fastdiv = 0x5; /* div by 66 */ 
		rules.clkdiv.ddiv    = CS4224_DDIV_DIV4;
		rules.clkdiv.rdiv    = CS4224_RDIV_DIV64;  
    /* Enable the fractional divider since 10664.22800/64 != 156.25 */
    /*   divisor   = floor(vco_rate/(8.0*ref_clk)) */
    /*             = floor(10664.22800/(8.0*156.25) */
    /*   numerator = round(pow(2.0,24)*((vco_rate/(8*ref_clk))-divisor,6) */
    /*             = round(16777216.000000 * (8.531382 - 8),6) */
    /*             = round(8915117.303398,6) */
    /* Double checking the calculation: */
    /*             = 8 * ref_clk * (divisor + (numerator/0xffffff)) */
    /*             = 8 * 156.250000 * (divisor + (0.531382) */
    /*             = 8 * 156.250000 * (8.531382) */
    /*             = 10664.228017 */
//    rules.fracdiv.enable    = TRUE;
//    rules.fracdiv.numerator = 0x8808ad;
//    rules.fracdiv.divisor   = 0x8;

    /* Configure the RDIV/DDIV parameters */
    rules.clkdiv.enable = TRUE;
    rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_4;

    /* Configuring the microcode image */
    rules.rx_if.dplx_line_edc_mode = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_edc_mode = CS_HSIO_EDC_MODE_SR;

    /* Configuring the trace loss settings */
    /* The driver trace loss may need further tuning for the target application */
    /* The equalizer trace loss is currently only used in SR mode or when the */
    /* microcode is disabled */
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;


    for(slice = 0; slice < CS4224_MAX_NUM_SLICES(0); slice=slice+2)
    {
        status |= cs4224_slice_enter_operational_state(slice, &rules);

    }

    return status;
}

cs_status cs4343_configure_otu1_inv()
{
    cs_status status = CS_OK; /* return code, CS_OK == good
                                              CS_ERROR == bad*/
    cs_uint32 slice = 0;      /* The slice/channel being configured*/
    cs4224_rules_t rules;     /* The configuration rules for initializing
                                 the device */
    e_cs4224_polarity_inv_t slice_inv[] = {  \
        /*0*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_HOST_TX) , \
        /*1*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*3*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*5*/ CS4224_POLARITY_INV_NONE , \
        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_RX, \
        /*7*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_RX | CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
    };

    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. The slice parameter is only
       passed so that the upper 24 bits can be used to reference the
       appropriate ASIC. The hard reset is necessary to properly
       initialize all registers since some are not clocked by default
       when the external reset pin is toggled. Do not call this method
       multiple times each time you configure a slice as it will reset
       the whole ASIC each time. It should only be done once on power up.*/
//    cs4224_hard_reset(slice);

    /* The cs4224_rules_set_default() method is used to default the rules */
    /* for a particular target application. They can be customized before */
    /* calling cs4224_slice_enter_operational_state() */
    /* Data rate = 2666.05700 MHz */
    /* Clock rate = 156.25 MHz */
    cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		rules.ref_clk_rate                     = 166.6285625;//CS4224_REF_CLK_106p25;
		rules.fracdiv.enable                   = FALSE; 
	  rules.clkdiv.enable = TRUE;
		rules.clkdiv.fastdiv = 0x5; /* div by 66 */ 
		rules.clkdiv.ddiv    = CS4224_DDIV_DIV4;
		rules.clkdiv.rdiv    = CS4224_RDIV_DIV64;  
    /* Enable the fractional divider since 10664.22800/64 != 156.25 */
    /*   divisor   = floor(vco_rate/(8.0*ref_clk)) */
    /*             = floor(10664.22800/(8.0*156.25) */
    /*   numerator = round(pow(2.0,24)*((vco_rate/(8*ref_clk))-divisor,6) */
    /*             = round(16777216.000000 * (8.531382 - 8),6) */
    /*             = round(8915117.303398,6) */
    /* Double checking the calculation: */
    /*             = 8 * ref_clk * (divisor + (numerator/0xffffff)) */
    /*             = 8 * 156.250000 * (divisor + (0.531382) */
    /*             = 8 * 156.250000 * (8.531382) */
    /*             = 10664.228017 */
//    rules.fracdiv.enable    = TRUE;
//    rules.fracdiv.numerator = 0x8808ad;
//    rules.fracdiv.divisor   = 0x8;

    /* Configure the RDIV/DDIV parameters */
    rules.clkdiv.enable = TRUE;
    rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_4;

    /* Configuring the microcode image */
    rules.rx_if.dplx_line_edc_mode = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_edc_mode = CS_HSIO_EDC_MODE_SR;

    /* Configuring the trace loss settings */
    /* The driver trace loss may need further tuning for the target application */
    /* The equalizer trace loss is currently only used in SR mode or when the */
    /* microcode is disabled */
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
		
		rules.enable_fec   = TRUE;


    for(slice = 0; slice < CS4224_MAX_NUM_SLICES(0); slice=slice+2)
    {
      rules.polarity_inv = slice_inv[slice];  
			status |= cs4224_slice_enter_operational_state(slice, &rules);
    }

    return status;
}

/* Configuration script for OTU1E    (11049.107Mbps)     traffic */
cs_status cs4343_configure_otu1e()
{
    cs_status status = CS_OK; /* return code, CS_OK == good
                                              CS_ERROR == bad*/
    cs_uint32 slice = 0;      /* The slice/channel being configured*/
    cs4224_rules_t rules;     /* The configuration rules for initializing
                                 the device */


    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. The slice parameter is only
       passed so that the upper 24 bits can be used to reference the
       appropriate ASIC. The hard reset is necessary to properly
       initialize all registers since some are not clocked by default
       when the external reset pin is toggled. Do not call this method
       multiple times each time you configure a slice as it will reset
       the whole ASIC each time. It should only be done once on power up.*/
//	cs4224_hard_reset(slice);

    /* The cs4224_rules_set_default() method is used to default the rules */
    /* for a particular target application. They can be customized before */
    /* calling cs4224_slice_enter_operational_state() */
    /* Data rate = 11049.10700 MHz */
    /* Clock rate = 156.25 MHz */
    cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		rules.ref_clk_rate                     = 110.49107;//CS4224_REF_CLK_106p25;
		rules.fracdiv.enable                   = FALSE; 
	  rules.clkdiv.enable = TRUE;
		rules.clkdiv.fastdiv = 0x5; /* div by 66 */ 
		rules.clkdiv.ddiv    = CS4224_DDIV_DIV1;
		rules.clkdiv.rdiv    = CS4224_RDIV_DIV100;  
    /* Enable the fractional divider since 11049.10700/64 != 156.25 */
    /*   divisor   = floor(vco_rate/(8.0*ref_clk)) */
    /*             = floor(11049.10700/(8.0*156.25) */
    /*   numerator = round(pow(2.0,24)*((vco_rate/(8*ref_clk))-divisor,6) */
    /*             = round(16777216.000000 * (8.839286 - 8),6) */
    /*             = round(14080875.796890,6) */
    /* Double checking the calculation: */
    /*             = 8 * ref_clk * (divisor + (numerator/0xffffff)) */
    /*             = 8 * 156.250000 * (divisor + (0.839286) */
    /*             = 8 * 156.250000 * (8.839286) */
    /*             = 11049.107003 */
//    rules.fracdiv.enable    = TRUE;
//    rules.fracdiv.numerator = 0xd6db6b;
//    rules.fracdiv.divisor   = 0x8;

    /* Configuring the microcode image */
    rules.rx_if.dplx_line_edc_mode = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_edc_mode = CS_HSIO_EDC_MODE_SR;

    /* Configuring the trace loss settings */
    /* The driver trace loss may need further tuning for the target application */
    /* The equalizer trace loss is currently only used in SR mode or when the */
    /* microcode is disabled */
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;


    for(slice = 0; slice < CS4224_MAX_NUM_SLICES(0); slice=slice+2)
    {
        status |= cs4224_slice_enter_operational_state(slice, &rules);

    }

    return status;
}

cs_status cs4343_configure_otu1e_inv()
{
    cs_status status = CS_OK; /* return code, CS_OK == good
                                              CS_ERROR == bad*/
    cs_uint32 slice = 0;      /* The slice/channel being configured*/
    cs4224_rules_t rules;     /* The configuration rules for initializing
                                 the device */
    e_cs4224_polarity_inv_t slice_inv[] = {  \
        /*0*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_HOST_TX) , \
        /*1*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*3*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*5*/ CS4224_POLARITY_INV_NONE , \
        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_RX, \
        /*7*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_RX | CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
    };

    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. The slice parameter is only
       passed so that the upper 24 bits can be used to reference the
       appropriate ASIC. The hard reset is necessary to properly
       initialize all registers since some are not clocked by default
       when the external reset pin is toggled. Do not call this method
       multiple times each time you configure a slice as it will reset
       the whole ASIC each time. It should only be done once on power up.*/
//    cs4224_hard_reset(slice);

    /* The cs4224_rules_set_default() method is used to default the rules */
    /* for a particular target application. They can be customized before */
    /* calling cs4224_slice_enter_operational_state() */
    /* Data rate = 11049.10700 MHz */
    /* Clock rate = 156.25 MHz */
    cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		rules.ref_clk_rate                     = 110.49107;//CS4224_REF_CLK_106p25;
		rules.fracdiv.enable                   = FALSE; 
	  rules.clkdiv.enable = TRUE;
		rules.clkdiv.fastdiv = 0x5; /* div by 66 */ 
		rules.clkdiv.ddiv    = CS4224_DDIV_DIV1;
		rules.clkdiv.rdiv    = CS4224_RDIV_DIV100;  
    /* Enable the fractional divider since 11049.10700/64 != 156.25 */
    /*   divisor   = floor(vco_rate/(8.0*ref_clk)) */
    /*             = floor(11049.10700/(8.0*156.25) */
    /*   numerator = round(pow(2.0,24)*((vco_rate/(8*ref_clk))-divisor,6) */
    /*             = round(16777216.000000 * (8.839286 - 8),6) */
    /*             = round(14080875.796890,6) */
    /* Double checking the calculation: */
    /*             = 8 * ref_clk * (divisor + (numerator/0xffffff)) */
    /*             = 8 * 156.250000 * (divisor + (0.839286) */
    /*             = 8 * 156.250000 * (8.839286) */
    /*             = 11049.107003 */
//    rules.fracdiv.enable    = TRUE;
//    rules.fracdiv.numerator = 0xd6db6b;
//    rules.fracdiv.divisor   = 0x8;

    /* Configuring the microcode image */
    rules.rx_if.dplx_line_edc_mode = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_edc_mode = CS_HSIO_EDC_MODE_SR;

    /* Configuring the trace loss settings */
    /* The driver trace loss may need further tuning for the target application */
    /* The equalizer trace loss is currently only used in SR mode or when the */
    /* microcode is disabled */
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
		
		rules.enable_fec   = TRUE;


    for(slice = 0; slice < CS4224_MAX_NUM_SLICES(0); slice=slice+2)
    {
      rules.polarity_inv = slice_inv[slice];  
			status |= cs4224_slice_enter_operational_state(slice, &rules);
    }

    return status;
}

/* Configuration script for OTU1F    (11270.089Mbps)     traffic */
cs_status cs4343_configure_otu1f()
{
    cs_status status = CS_OK; /* return code, CS_OK == good
                                              CS_ERROR == bad*/
    cs_uint32 slice = 0;      /* The slice/channel being configured*/
    cs4224_rules_t rules;     /* The configuration rules for initializing
                                 the device */


    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. The slice parameter is only
       passed so that the upper 24 bits can be used to reference the
       appropriate ASIC. The hard reset is necessary to properly
       initialize all registers since some are not clocked by default
       when the external reset pin is toggled. Do not call this method
       multiple times each time you configure a slice as it will reset
       the whole ASIC each time. It should only be done once on power up.*/
//    cs4224_hard_reset(slice);

    /* The cs4224_rules_set_default() method is used to default the rules */
    /* for a particular target application. They can be customized before */
    /* calling cs4224_slice_enter_operational_state() */
    /* Data rate = 11270.08900 MHz */
    /* Clock rate = 156.25 MHz */
    cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		rules.ref_clk_rate                     = 112.70089;//CS4224_REF_CLK_106p25;
		rules.fracdiv.enable                   = FALSE; 
	  rules.clkdiv.enable = TRUE;
		rules.clkdiv.fastdiv = 0x5; /* div by 66 */ 
		rules.clkdiv.ddiv    = CS4224_DDIV_DIV1;
		rules.clkdiv.rdiv    = CS4224_RDIV_DIV100;  
    /* Enable the fractional divider since 11270.08900/64 != 156.25 */
    /*   divisor   = floor(vco_rate/(8.0*ref_clk)) */
    /*             = floor(11270.08900/(8.0*156.25) */
    /*   numerator = round(pow(2.0,24)*((vco_rate/(8*ref_clk))-divisor,6) */
    /*             = round(16777216.000000 * (9.016071 - 9),6) */
    /*             = round(269629.993779,6) */
    /* Double checking the calculation: */
    /*             = 8 * ref_clk * (divisor + (numerator/0xffffff)) */
    /*             = 8 * 156.250000 * (divisor + (0.016071) */
    /*             = 8 * 156.250000 * (9.016071) */
    /*             = 11270.088927 */
//    rules.fracdiv.enable    = TRUE;
//    rules.fracdiv.numerator = 0x41d3d;
//    rules.fracdiv.divisor   = 0x9;

    /* Configuring the microcode image */
    rules.rx_if.dplx_line_edc_mode = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_edc_mode = CS_HSIO_EDC_MODE_SR;

    /* Configuring the trace loss settings */
    /* The driver trace loss may need further tuning for the target application */
    /* The equalizer trace loss is currently only used in SR mode or when the */
    /* microcode is disabled */
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;


    for(slice = 0; slice < CS4224_MAX_NUM_SLICES(0); slice=slice+2)
    {
        status |= cs4224_slice_enter_operational_state(slice, &rules);

    }

    return status;
}

cs_status cs4343_configure_otu1f_inv()
{
    cs_status status = CS_OK; /* return code, CS_OK == good
                                              CS_ERROR == bad*/
    cs_uint32 slice = 0;      /* The slice/channel being configured*/
    cs4224_rules_t rules;     /* The configuration rules for initializing
                                 the device */
    e_cs4224_polarity_inv_t slice_inv[] = {  \
        /*0*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_HOST_TX) , \
        /*1*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*3*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*5*/ CS4224_POLARITY_INV_NONE , \
        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_RX, \
        /*7*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_RX | CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
    };

    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. The slice parameter is only
       passed so that the upper 24 bits can be used to reference the
       appropriate ASIC. The hard reset is necessary to properly
       initialize all registers since some are not clocked by default
       when the external reset pin is toggled. Do not call this method
       multiple times each time you configure a slice as it will reset
       the whole ASIC each time. It should only be done once on power up.*/
//    cs4224_hard_reset(slice);

    /* The cs4224_rules_set_default() method is used to default the rules */
    /* for a particular target application. They can be customized before */
    /* calling cs4224_slice_enter_operational_state() */
    /* Data rate = 11270.08900 MHz */
    /* Clock rate = 156.25 MHz */
    cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		rules.ref_clk_rate                     = 112.70089;//CS4224_REF_CLK_106p25;
		rules.fracdiv.enable                   = FALSE; 
	  rules.clkdiv.enable = TRUE;
		rules.clkdiv.fastdiv = 0x5; /* div by 66 */ 
		rules.clkdiv.ddiv    = CS4224_DDIV_DIV1;
		rules.clkdiv.rdiv    = CS4224_RDIV_DIV100;  
    /* Enable the fractional divider since 11270.08900/64 != 156.25 */
    /*   divisor   = floor(vco_rate/(8.0*ref_clk)) */
    /*             = floor(11270.08900/(8.0*156.25) */
    /*   numerator = round(pow(2.0,24)*((vco_rate/(8*ref_clk))-divisor,6) */
    /*             = round(16777216.000000 * (9.016071 - 9),6) */
    /*             = round(269629.993779,6) */
    /* Double checking the calculation: */
    /*             = 8 * ref_clk * (divisor + (numerator/0xffffff)) */
    /*             = 8 * 156.250000 * (divisor + (0.016071) */
    /*             = 8 * 156.250000 * (9.016071) */
    /*             = 11270.088927 */
//    rules.fracdiv.enable    = TRUE;
//    rules.fracdiv.numerator = 0x41d3d;
//    rules.fracdiv.divisor   = 0x9;

    /* Configuring the microcode image */
    rules.rx_if.dplx_line_edc_mode = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_edc_mode = CS_HSIO_EDC_MODE_SR;

    /* Configuring the trace loss settings */
    /* The driver trace loss may need further tuning for the target application */
    /* The equalizer trace loss is currently only used in SR mode or when the */
    /* microcode is disabled */
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
		
		rules.enable_fec   = TRUE;


    for(slice = 0; slice < CS4224_MAX_NUM_SLICES(0); slice=slice+2)
    {
      rules.polarity_inv = slice_inv[slice];  
			status |= cs4224_slice_enter_operational_state(slice, &rules);
    }

    return status;
}

/* Configuration script for OTU2     (10709.22532Mbps)     traffic */
cs_status Rules_Set_otu2()
{
    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;
//    e_cs4224_polarity_inv_t slice_inv[] = {  \
//        /*0*/ CS4224_POLARITY_INV_DPLX_LINE_TX, \
//        /*1*/ CS4224_POLARITY_INV_NONE, \
//        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_TX , \
//        /*3*/ CS4224_POLARITY_INV_NONE , \
//        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_TX , \
//        /*5*/ CS4224_POLARITY_INV_NONE , \
//        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_TX, \
//        /*7*/ CS4224_POLARITY_INV_NONE , \
//    };
    //CS_PRINTF(("Initializing the chip in oc48 mode\n"));

    status |= cs4224_hard_reset(slice);
    if(CS_OK != status)
    {
      //CS_TRACE(("ERROR trying to reset device\n"));
        return status;
    }
 
    cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
//		rules.ref_clk_rate                     = 167.331645625;//CS4224_REF_CLK_106p25;
//		rules.fracdiv.enable                   = FALSE; 
//	  rules.clkdiv.enable = TRUE;
//		rules.clkdiv.fastdiv = 0x5; /* div by 66 */ 
//		rules.clkdiv.ddiv    = CS4224_DDIV_DIV1;
//		rules.clkdiv.rdiv    = CS4224_RDIV_DIV64;  
				rules.ref_clk_rate                     = (float)f_si570[rate]/rdiv[DIV_Rate[rate].rdivpos]*ddiv[DIV_Rate[rate].ddivpos];//CS4224_REF_CLK_155p52;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = DIV_Rate[rate].ddivpos;//CS4224_CLKDIV_RDIV_BY_64;
    rules.clkdiv.rdiv = DIV_Rate[rate].rdivpos;
    /* Enable the fractional divider since 10709.22532/64 != 156.25 */
    /*   divisor   = floor(vco_rate/(8.0*ref_clk)) */
    /*             = floor(10709.22532/(8.0*156.25) */
    /*   numerator = round(pow(2.0,24)*((vco_rate/(8*ref_clk))-divisor,6) */
    /*             = round(16777216.000000 * (8.567380 - 8),6) */
    /*             = round(9519061.109047,6) */
    /* Double checking the calculation: */
    /*             = 8 * ref_clk * (divisor + (numerator/0xffffff)) */
    /*             = 8 * 156.250000 * (divisor + (0.567380) */
    /*             = 8 * 156.250000 * (8.567380) */
    /*             = 10709.225354 */
//    rules.fracdiv.enable    = TRUE;
//    rules.fracdiv.numerator = 0x913fd5;
//    rules.fracdiv.divisor   = 0x8;

    /* Configuring the microcode image */
    rules.rx_if.dplx_line_edc_mode = CS_HSIO_EDC_MODE_CX1;
    rules.rx_if.dplx_host_edc_mode = CS_HSIO_EDC_MODE_CX1;

    /* Configuring the trace loss settings */
    /* The driver trace loss may need further tuning for the target application */
    /* The equalizer trace loss is currently only used in SR mode or when the */
    /* microcode is disabled */
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    
		for(slice = 2; slice < 6; slice=slice+2)
    {
//      if(polarity_ch[slice/2])  
//			{
//				rules.enable_fec   = TRUE; 
//				rules.polarity_inv = slice_inv[slice];
//        /* Initialize the slice and bring it into operational state */
//        status |=cs4224_slice_enter_operational_state(slice, &rules);
//			}
//			else
				status |= cs4224_slice_enter_operational_state(slice, &rules);
    }
		cs4224_slice_power_down(0);
		cs4224_slice_power_down(1);
		cs4224_slice_power_down(3);
		cs4224_slice_power_down(5);
		cs4224_slice_power_down(6);
		cs4224_slice_power_down(7);

    return status;		
}

cs_status cs4343_configure_otu2(int chan)
{
    cs_status status = CS_OK; /* return code, CS_OK == good
                                              CS_ERROR == bad*/
    cs_uint32 slice = 0;      /* The slice/channel being configured*/
    cs4224_rules_t rules;     /* The configuration rules for initializing
                                 the device */


    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. The slice parameter is only
       passed so that the upper 24 bits can be used to reference the
       appropriate ASIC. The hard reset is necessary to properly
       initialize all registers since some are not clocked by default
       when the external reset pin is toggled. Do not call this method
       multiple times each time you configure a slice as it will reset
       the whole ASIC each time. It should only be done once on power up.*/
//    cs4224_hard_reset(slice);

    /* The cs4224_rules_set_default() method is used to default the rules */
    /* for a particular target application. They can be customized before */
    /* calling cs4224_slice_enter_operational_state() */
    /* Data rate = 10709.22532 MHz */
    /* Clock rate = 156.25 MHz */
    cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		rules.ref_clk_rate                     = 167.331645625;//CS4224_REF_CLK_106p25;
		rules.fracdiv.enable                   = FALSE; 
	  rules.clkdiv.enable = TRUE;
		rules.clkdiv.fastdiv = 0x5; /* div by 66 */ 
		rules.clkdiv.ddiv    = CS4224_DDIV_DIV1;
		rules.clkdiv.rdiv    = CS4224_RDIV_DIV64;  
    /* Enable the fractional divider since 10709.22532/64 != 156.25 */
    /*   divisor   = floor(vco_rate/(8.0*ref_clk)) */
    /*             = floor(10709.22532/(8.0*156.25) */
    /*   numerator = round(pow(2.0,24)*((vco_rate/(8*ref_clk))-divisor,6) */
    /*             = round(16777216.000000 * (8.567380 - 8),6) */
    /*             = round(9519061.109047,6) */
    /* Double checking the calculation: */
    /*             = 8 * ref_clk * (divisor + (numerator/0xffffff)) */
    /*             = 8 * 156.250000 * (divisor + (0.567380) */
    /*             = 8 * 156.250000 * (8.567380) */
    /*             = 10709.225354 */
//    rules.fracdiv.enable    = TRUE;
//    rules.fracdiv.numerator = 0x913fd5;
//    rules.fracdiv.divisor   = 0x8;

    /* Configuring the microcode image */
    rules.rx_if.dplx_line_edc_mode = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_edc_mode = CS_HSIO_EDC_MODE_SR;

    /* Configuring the trace loss settings */
    /* The driver trace loss may need further tuning for the target application */
    /* The equalizer trace loss is currently only used in SR mode or when the */
    /* microcode is disabled */
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;


//    for(slice = 0; slice < 8; slice=slice+2)
//    {
        status |= cs4224_slice_enter_operational_state(chan*2, &rules);

//    }

    return status;
}

cs_status cs4343_configure_otu2_inv(int chan)
{
    cs_status status = CS_OK; /* return code, CS_OK == good
                                              CS_ERROR == bad*/
    cs_uint32 slice = 0;      /* The slice/channel being configured*/
    cs4224_rules_t rules;     /* The configuration rules for initializing
                                 the device */
    e_cs4224_polarity_inv_t slice_inv[] = {  \
        /*0*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_HOST_TX) , \
        /*1*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*3*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*5*/ CS4224_POLARITY_INV_NONE , \
        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_RX, \
        /*7*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_RX | CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
    };

    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. The slice parameter is only
       passed so that the upper 24 bits can be used to reference the
       appropriate ASIC. The hard reset is necessary to properly
       initialize all registers since some are not clocked by default
       when the external reset pin is toggled. Do not call this method
       multiple times each time you configure a slice as it will reset
       the whole ASIC each time. It should only be done once on power up.*/
//    cs4224_hard_reset(slice);

    /* The cs4224_rules_set_default() method is used to default the rules */
    /* for a particular target application. They can be customized before */
    /* calling cs4224_slice_enter_operational_state() */
    /* Data rate = 10709.22532 MHz */
    /* Clock rate = 156.25 MHz */
    cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
//		rules.ref_clk_rate                     = 107.0922532;//CS4224_REF_CLK_106p25;
//		rules.fracdiv.enable                   = FALSE; 
//	  rules.clkdiv.enable = TRUE;
//		rules.clkdiv.fastdiv = 0x5; /* div by 66 */ 
//		rules.clkdiv.ddiv    = CS4224_DDIV_DIV1;
//		rules.clkdiv.rdiv    = CS4224_RDIV_DIV100;  
		rules.ref_clk_rate                     = 167.331645625;//CS4224_REF_CLK_106p25;
		rules.fracdiv.enable                   = FALSE; 
	  rules.clkdiv.enable = TRUE;
		rules.clkdiv.fastdiv = 0x5; /* div by 66 */ 
		rules.clkdiv.ddiv    = CS4224_DDIV_DIV1;
		rules.clkdiv.rdiv    = CS4224_RDIV_DIV64; 
    /* Enable the fractional divider since 10709.22532/64 != 156.25 */
    /*   divisor   = floor(vco_rate/(8.0*ref_clk)) */
    /*             = floor(10709.22532/(8.0*156.25) */
    /*   numerator = round(pow(2.0,24)*((vco_rate/(8*ref_clk))-divisor,6) */
    /*             = round(16777216.000000 * (8.567380 - 8),6) */
    /*             = round(9519061.109047,6) */
    /* Double checking the calculation: */
    /*             = 8 * ref_clk * (divisor + (numerator/0xffffff)) */
    /*             = 8 * 156.250000 * (divisor + (0.567380) */
    /*             = 8 * 156.250000 * (8.567380) */
    /*             = 10709.225354 */
//    rules.fracdiv.enable    = TRUE;
//    rules.fracdiv.numerator = 0x913fd5;
//    rules.fracdiv.divisor   = 0x8;

    /* Configuring the microcode image */
    rules.rx_if.dplx_line_edc_mode = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_edc_mode = CS_HSIO_EDC_MODE_SR;

    /* Configuring the trace loss settings */
    /* The driver trace loss may need further tuning for the target application */
    /* The equalizer trace loss is currently only used in SR mode or when the */
    /* microcode is disabled */
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
		
		rules.enable_fec   = TRUE;

//    for(slice = 0; slice < 8; slice=slice+2)
//    {
      rules.polarity_inv = slice_inv[chan*2];  
			status |= cs4224_slice_enter_operational_state(chan*2, &rules);
//    }

    return status;
}


/* Configuration script for OTU2E    (11095.727Mbps)     traffic */
cs_status Rules_Set_otu2e()
{
    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;
//    e_cs4224_polarity_inv_t slice_inv[] = {  \
//        /*0*/ CS4224_POLARITY_INV_DPLX_LINE_TX, \
//        /*1*/ CS4224_POLARITY_INV_NONE, \
//        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_TX , \
//        /*3*/ CS4224_POLARITY_INV_NONE , \
//        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_TX , \
//        /*5*/ CS4224_POLARITY_INV_NONE , \
//        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_TX, \
//        /*7*/ CS4224_POLARITY_INV_NONE , \
//    };
    //CS_PRINTF(("Initializing the chip in oc48 mode\n"));

    status |= cs4224_hard_reset(slice);
    if(CS_OK != status)
    {
      //CS_TRACE(("ERROR trying to reset device\n"));
        return status;
    }
 
    cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
//		rules.ref_clk_rate                     = 173.370734375;//CS4224_REF_CLK_106p25;
//		rules.fracdiv.enable                   = FALSE; 
//	  rules.clkdiv.enable = TRUE;
//		rules.clkdiv.fastdiv = 0x5; /* div by 66 */ 
//		rules.clkdiv.ddiv    = CS4224_DDIV_DIV1;
//		rules.clkdiv.rdiv    = CS4224_RDIV_DIV64;  
				rules.ref_clk_rate                     = (float)f_si570[rate]/rdiv[DIV_Rate[rate].rdivpos]*ddiv[DIV_Rate[rate].ddivpos];//CS4224_REF_CLK_155p52;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = DIV_Rate[rate].ddivpos;//CS4224_CLKDIV_RDIV_BY_64;
    rules.clkdiv.rdiv = DIV_Rate[rate].rdivpos;
    /* Enable the fractional divider since 11095.72700/64 != 156.25 */
    /*   divisor   = floor(vco_rate/(8.0*ref_clk)) */
    /*             = floor(11095.72700/(8.0*156.25) */
    /*   numerator = round(pow(2.0,24)*((vco_rate/(8*ref_clk))-divisor,6) */
    /*             = round(16777216.000000 * (8.876582 - 8),6) */
    /*             = round(14706598.844826,6) */
    /* Double checking the calculation: */
    /*             = 8 * ref_clk * (divisor + (numerator/0xffffff)) */
    /*             = 8 * 156.250000 * (divisor + (0.876582) */
    /*             = 8 * 156.250000 * (8.876582) */
    /*             = 11095.727002 */
//    rules.fracdiv.enable    = TRUE;
//    rules.fracdiv.numerator = 0xe067a6;
//    rules.fracdiv.divisor   = 0x8;

    /* Configuring the microcode image */
    rules.rx_if.dplx_line_edc_mode = CS_HSIO_EDC_MODE_CX1;
    rules.rx_if.dplx_host_edc_mode = CS_HSIO_EDC_MODE_CX1;

    /* Configuring the trace loss settings */
    /* The driver trace loss may need further tuning for the target application */
    /* The equalizer trace loss is currently only used in SR mode or when the */
    /* microcode is disabled */
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    
		for(slice = 2; slice < 6; slice=slice+2)
    {
//      if(polarity_ch[slice/2])  
//			{
//				rules.enable_fec   = TRUE; 
//				rules.polarity_inv = slice_inv[slice];
//        /* Initialize the slice and bring it into operational state */
//        status |=cs4224_slice_enter_operational_state(slice, &rules);
//			}
//			else
				status |= cs4224_slice_enter_operational_state(slice, &rules);
    }
		cs4224_slice_power_down(0);
		cs4224_slice_power_down(1);
		cs4224_slice_power_down(3);
		cs4224_slice_power_down(5);
		cs4224_slice_power_down(6);
		cs4224_slice_power_down(7);

    return status;		
}

cs_status cs4343_configure_otu2e(int chan)
{
    cs_status status = CS_OK; /* return code, CS_OK == good
                                              CS_ERROR == bad*/
    cs_uint32 slice = 0;      /* The slice/channel being configured*/
    cs4224_rules_t rules;     /* The configuration rules for initializing
                                 the device */


    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. The slice parameter is only
       passed so that the upper 24 bits can be used to reference the
       appropriate ASIC. The hard reset is necessary to properly
       initialize all registers since some are not clocked by default
       when the external reset pin is toggled. Do not call this method
       multiple times each time you configure a slice as it will reset
       the whole ASIC each time. It should only be done once on power up.*/
//    cs4224_hard_reset(slice);

    /* The cs4224_rules_set_default() method is used to default the rules */
    /* for a particular target application. They can be customized before */
    /* calling cs4224_slice_enter_operational_state() */
    /* Data rate = 11095.72700 MHz */
    /* Clock rate = 156.25 MHz */
    cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		rules.ref_clk_rate                     = 173.370734375;//CS4224_REF_CLK_106p25;
		rules.fracdiv.enable                   = FALSE; 
	  rules.clkdiv.enable = TRUE;
		rules.clkdiv.fastdiv = 0x5; /* div by 66 */ 
		rules.clkdiv.ddiv    = CS4224_DDIV_DIV1;
		rules.clkdiv.rdiv    = CS4224_RDIV_DIV64;  
    /* Enable the fractional divider since 11095.72700/64 != 156.25 */
    /*   divisor   = floor(vco_rate/(8.0*ref_clk)) */
    /*             = floor(11095.72700/(8.0*156.25) */
    /*   numerator = round(pow(2.0,24)*((vco_rate/(8*ref_clk))-divisor,6) */
    /*             = round(16777216.000000 * (8.876582 - 8),6) */
    /*             = round(14706598.844826,6) */
    /* Double checking the calculation: */
    /*             = 8 * ref_clk * (divisor + (numerator/0xffffff)) */
    /*             = 8 * 156.250000 * (divisor + (0.876582) */
    /*             = 8 * 156.250000 * (8.876582) */
    /*             = 11095.727002 */
//    rules.fracdiv.enable    = TRUE;
//    rules.fracdiv.numerator = 0xe067a6;
//    rules.fracdiv.divisor   = 0x8;

    /* Configuring the microcode image */
    rules.rx_if.dplx_line_edc_mode = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_edc_mode = CS_HSIO_EDC_MODE_SR;

    /* Configuring the trace loss settings */
    /* The driver trace loss may need further tuning for the target application */
    /* The equalizer trace loss is currently only used in SR mode or when the */
    /* microcode is disabled */
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;


//    for(slice = 0; slice < 8; slice=slice+2)
//    {
        status |= cs4224_slice_enter_operational_state(chan*2, &rules);

//    }

    return status;
}

cs_status cs4343_configure_otu2e_inv(int chan)
{
    cs_status status = CS_OK; /* return code, CS_OK == good
                                              CS_ERROR == bad*/
    cs_uint32 slice = 0;      /* The slice/channel being configured*/
    cs4224_rules_t rules;     /* The configuration rules for initializing
                                 the device */
    e_cs4224_polarity_inv_t slice_inv[] = {  \
        /*0*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_HOST_TX) , \
        /*1*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*3*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*5*/ CS4224_POLARITY_INV_NONE , \
        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_RX, \
        /*7*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_RX | CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
    };

    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. The slice parameter is only
       passed so that the upper 24 bits can be used to reference the
       appropriate ASIC. The hard reset is necessary to properly
       initialize all registers since some are not clocked by default
       when the external reset pin is toggled. Do not call this method
       multiple times each time you configure a slice as it will reset
       the whole ASIC each time. It should only be done once on power up.*/
//    cs4224_hard_reset(slice);

    /* The cs4224_rules_set_default() method is used to default the rules */
    /* for a particular target application. They can be customized before */
    /* calling cs4224_slice_enter_operational_state() */
    /* Data rate = 11095.72700 MHz */
    /* Clock rate = 156.25 MHz */
    cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
//		rules.ref_clk_rate                     = 110.95727;//CS4224_REF_CLK_106p25;
//		rules.fracdiv.enable                   = FALSE; 
//	  rules.clkdiv.enable = TRUE;
//		rules.clkdiv.fastdiv = 0x5; /* div by 66 */ 
//		rules.clkdiv.ddiv    = CS4224_DDIV_DIV1;
//		rules.clkdiv.rdiv    = CS4224_RDIV_DIV100;  
		rules.ref_clk_rate                     = 173.370734375;//CS4224_REF_CLK_106p25;
		rules.fracdiv.enable                   = FALSE; 
	  rules.clkdiv.enable = TRUE;
		rules.clkdiv.fastdiv = 0x5; /* div by 66 */ 
		rules.clkdiv.ddiv    = CS4224_DDIV_DIV1;
		rules.clkdiv.rdiv    = CS4224_RDIV_DIV64;  
    /* Enable the fractional divider since 11095.72700/64 != 156.25 */
    /*   divisor   = floor(vco_rate/(8.0*ref_clk)) */
    /*             = floor(11095.72700/(8.0*156.25) */
    /*   numerator = round(pow(2.0,24)*((vco_rate/(8*ref_clk))-divisor,6) */
    /*             = round(16777216.000000 * (8.876582 - 8),6) */
    /*             = round(14706598.844826,6) */
    /* Double checking the calculation: */
    /*             = 8 * ref_clk * (divisor + (numerator/0xffffff)) */
    /*             = 8 * 156.250000 * (divisor + (0.876582) */
    /*             = 8 * 156.250000 * (8.876582) */
    /*             = 11095.727002 */
//    rules.fracdiv.enable    = TRUE;
//    rules.fracdiv.numerator = 0xe067a6;
//    rules.fracdiv.divisor   = 0x8;

    /* Configuring the microcode image */
    rules.rx_if.dplx_line_edc_mode = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_edc_mode = CS_HSIO_EDC_MODE_SR;

    /* Configuring the trace loss settings */
    /* The driver trace loss may need further tuning for the target application */
    /* The equalizer trace loss is currently only used in SR mode or when the */
    /* microcode is disabled */
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
		
		rules.enable_fec   = TRUE;

//    for(slice = 0; slice < 8; slice=slice+2)
//    {
      rules.polarity_inv = slice_inv[chan*2];  
			status |= cs4224_slice_enter_operational_state(chan*2, &rules);
//    }

    return status;
}

/* Configuration script for OTU2F    (11317.642Mbps)     traffic */
cs_status Rules_Set_otu2f()
{
    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32 slice = 0;
//    e_cs4224_polarity_inv_t slice_inv[] = {  \
//        /*0*/ CS4224_POLARITY_INV_DPLX_LINE_TX, \
//        /*1*/ CS4224_POLARITY_INV_NONE, \
//        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_TX , \
//        /*3*/ CS4224_POLARITY_INV_NONE , \
//        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_TX , \
//        /*5*/ CS4224_POLARITY_INV_NONE , \
//        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_TX, \
//        /*7*/ CS4224_POLARITY_INV_NONE , \
//    };
    //CS_PRINTF(("Initializing the chip in oc48 mode\n"));

    status |= cs4224_hard_reset(slice);
    if(CS_OK != status)
    {
      //CS_TRACE(("ERROR trying to reset device\n"));
        return status;
    }
 
    cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
//		rules.ref_clk_rate                     = 176.83815625;//CS4224_REF_CLK_106p25;
//		rules.fracdiv.enable                   = FALSE; 
//	  rules.clkdiv.enable = TRUE;
//		rules.clkdiv.fastdiv = 0x5; /* div by 66 */ 
//		rules.clkdiv.ddiv    = CS4224_DDIV_DIV1;
//		rules.clkdiv.rdiv    = CS4224_RDIV_DIV64;  
				rules.ref_clk_rate                     = (float)f_si570[rate]/rdiv[DIV_Rate[rate].rdivpos]*ddiv[DIV_Rate[rate].ddivpos];//CS4224_REF_CLK_155p52;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = DIV_Rate[rate].ddivpos;//CS4224_CLKDIV_RDIV_BY_64;
    rules.clkdiv.rdiv = DIV_Rate[rate].rdivpos;
    /* Enable the fractional divider since 11317.64200/64 != 156.25 */
    /*   divisor   = floor(vco_rate/(8.0*ref_clk)) */
    /*             = floor(11317.64200/(8.0*156.25) */
    /*   numerator = round(pow(2.0,24)*((vco_rate/(8*ref_clk))-divisor,6) */
    /*             = round(16777216.000000 * (9.054114 - 9),6) */
    /*             = round(907875.555738,6) */
    /* Double checking the calculation: */
    /*             = 8 * ref_clk * (divisor + (numerator/0xffffff)) */
    /*             = 8 * 156.250000 * (divisor + (0.054114) */
    /*             = 8 * 156.250000 * (9.054114) */
    /*             = 11317.641963 */
//    rules.fracdiv.enable    = TRUE;
//    rules.fracdiv.numerator = 0xdda63;
//    rules.fracdiv.divisor   = 0x9;

    /* Configuring the microcode image */
    rules.rx_if.dplx_line_edc_mode = CS_HSIO_EDC_MODE_CX1;//CS_HSIO_EDC_MODE_CX1;
    rules.rx_if.dplx_host_edc_mode = CS_HSIO_EDC_MODE_CX1;//CS_HSIO_EDC_MODE_CX1;

    /* Configuring the trace loss settings */
    /* The driver trace loss may need further tuning for the target application */
    /* The equalizer trace loss is currently only used in SR mode or when the */
    /* microcode is disabled */
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
		
		rules.enable_fec   = TRUE;
    
		for(slice = 2; slice < 6; slice=slice+2)
    {
//      if(polarity_ch[slice/2])  
//			{
//				rules.enable_fec   = TRUE; 
//				rules.polarity_inv = slice_inv[slice];
//        /* Initialize the slice and bring it into operational state */
//        status |=cs4224_slice_enter_operational_state(slice, &rules);
//			}
//			else
				status |= cs4224_slice_enter_operational_state(slice, &rules);
    }
		cs4224_slice_power_down(0);
		cs4224_slice_power_down(1);
		cs4224_slice_power_down(3);
		cs4224_slice_power_down(5);
		cs4224_slice_power_down(6);
		cs4224_slice_power_down(7);

    return status;		
}

cs_status cs4343_configure_otu2f(int chan)
{
    cs_status status = CS_OK; /* return code, CS_OK == good
                                              CS_ERROR == bad*/
    cs_uint32 slice = 0;      /* The slice/channel being configured*/
    cs4224_rules_t rules;     /* The configuration rules for initializing
                                 the device */


    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. The slice parameter is only
       passed so that the upper 24 bits can be used to reference the
       appropriate ASIC. The hard reset is necessary to properly
       initialize all registers since some are not clocked by default
       when the external reset pin is toggled. Do not call this method
       multiple times each time you configure a slice as it will reset
       the whole ASIC each time. It should only be done once on power up.*/
//    cs4224_hard_reset(slice);

    /* The cs4224_rules_set_default() method is used to default the rules */
    /* for a particular target application. They can be customized before */
    /* calling cs4224_slice_enter_operational_state() */
    /* Data rate = 11317.64200 MHz */
    /* Clock rate = 156.25 MHz */
    cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		rules.ref_clk_rate                     = 176.83815625;//CS4224_REF_CLK_106p25;
		rules.fracdiv.enable                   = FALSE; 
	  rules.clkdiv.enable = TRUE;
		rules.clkdiv.fastdiv = 0x5; /* div by 66 */ 
		rules.clkdiv.ddiv    = CS4224_DDIV_DIV1;
		rules.clkdiv.rdiv    = CS4224_RDIV_DIV64;  
    /* Enable the fractional divider since 11317.64200/64 != 156.25 */
    /*   divisor   = floor(vco_rate/(8.0*ref_clk)) */
    /*             = floor(11317.64200/(8.0*156.25) */
    /*   numerator = round(pow(2.0,24)*((vco_rate/(8*ref_clk))-divisor,6) */
    /*             = round(16777216.000000 * (9.054114 - 9),6) */
    /*             = round(907875.555738,6) */
    /* Double checking the calculation: */
    /*             = 8 * ref_clk * (divisor + (numerator/0xffffff)) */
    /*             = 8 * 156.250000 * (divisor + (0.054114) */
    /*             = 8 * 156.250000 * (9.054114) */
    /*             = 11317.641963 */
//    rules.fracdiv.enable    = TRUE;
//    rules.fracdiv.numerator = 0xdda63;
//    rules.fracdiv.divisor   = 0x9;

    /* Configuring the microcode image */
    rules.rx_if.dplx_line_edc_mode = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_edc_mode = CS_HSIO_EDC_MODE_SR;

    /* Configuring the trace loss settings */
    /* The driver trace loss may need further tuning for the target application */
    /* The equalizer trace loss is currently only used in SR mode or when the */
    /* microcode is disabled */
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;


//    for(slice = 0; slice < 8; slice=slice+2)
//    {
        status |= cs4224_slice_enter_operational_state(chan*2, &rules);

//    }

    return status;
}

cs_status cs4343_configure_otu2f_inv(int chan)
{
    cs_status status = CS_OK; /* return code, CS_OK == good
                                              CS_ERROR == bad*/
    cs_uint32 slice = 0;      /* The slice/channel being configured*/
    cs4224_rules_t rules;     /* The configuration rules for initializing
                                 the device */
    e_cs4224_polarity_inv_t slice_inv[] = {  \
        /*0*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_HOST_TX) , \
        /*1*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*3*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*5*/ CS4224_POLARITY_INV_NONE , \
        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_RX, \
        /*7*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_RX | CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
    };

    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. The slice parameter is only
       passed so that the upper 24 bits can be used to reference the
       appropriate ASIC. The hard reset is necessary to properly
       initialize all registers since some are not clocked by default
       when the external reset pin is toggled. Do not call this method
       multiple times each time you configure a slice as it will reset
       the whole ASIC each time. It should only be done once on power up.*/
//    cs4224_hard_reset(slice);

    /* The cs4224_rules_set_default() method is used to default the rules */
    /* for a particular target application. They can be customized before */
    /* calling cs4224_slice_enter_operational_state() */
    /* Data rate = 11317.64200 MHz */
    /* Clock rate = 156.25 MHz */
    cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
//		rules.ref_clk_rate                     = 113.17642;//CS4224_REF_CLK_106p25;
//		rules.fracdiv.enable                   = FALSE; 
//	  rules.clkdiv.enable = TRUE;
//		rules.clkdiv.fastdiv = 0x5; /* div by 66 */ 
//		rules.clkdiv.ddiv    = CS4224_DDIV_DIV1;
//		rules.clkdiv.rdiv    = CS4224_RDIV_DIV100; 
		rules.ref_clk_rate                     = 176.83815625;//CS4224_REF_CLK_106p25;
		rules.fracdiv.enable                   = FALSE; 
	  rules.clkdiv.enable = TRUE;
		rules.clkdiv.fastdiv = 0x5; /* div by 66 */ 
		rules.clkdiv.ddiv    = CS4224_DDIV_DIV1;
		rules.clkdiv.rdiv    = CS4224_RDIV_DIV64;  
    /* Enable the fractional divider since 11317.64200/64 != 156.25 */
    /*   divisor   = floor(vco_rate/(8.0*ref_clk)) */
    /*             = floor(11317.64200/(8.0*156.25) */
    /*   numerator = round(pow(2.0,24)*((vco_rate/(8*ref_clk))-divisor,6) */
    /*             = round(16777216.000000 * (9.054114 - 9),6) */
    /*             = round(907875.555738,6) */
    /* Double checking the calculation: */
    /*             = 8 * ref_clk * (divisor + (numerator/0xffffff)) */
    /*             = 8 * 156.250000 * (divisor + (0.054114) */
    /*             = 8 * 156.250000 * (9.054114) */
    /*             = 11317.641963 */
//    rules.fracdiv.enable    = TRUE;
//    rules.fracdiv.numerator = 0xdda63;
//    rules.fracdiv.divisor   = 0x9;

    /* Configuring the microcode image */
    rules.rx_if.dplx_line_edc_mode = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_edc_mode = CS_HSIO_EDC_MODE_SR;

    /* Configuring the trace loss settings */
    /* The driver trace loss may need further tuning for the target application */
    /* The equalizer trace loss is currently only used in SR mode or when the */
    /* microcode is disabled */
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
		
		rules.enable_fec   = TRUE;

//    for(slice = 0; slice < 8; slice=slice+2)
//    {
      rules.polarity_inv = slice_inv[chan*2];  
			status |= cs4224_slice_enter_operational_state(chan*2, &rules);
//    }

    return status;
}


/* Configuration script for OTU3     (10754.60325Mbps)     traffic */
cs_status cs4343_configure_otu3()
{
    cs_status status = CS_OK; /* return code, CS_OK == good
                                              CS_ERROR == bad*/
    cs_uint32 slice = 0;      /* The slice/channel being configured*/
    cs4224_rules_t rules;     /* The configuration rules for initializing
                                 the device */


    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. The slice parameter is only
       passed so that the upper 24 bits can be used to reference the
       appropriate ASIC. The hard reset is necessary to properly
       initialize all registers since some are not clocked by default
       when the external reset pin is toggled. Do not call this method
       multiple times each time you configure a slice as it will reset
       the whole ASIC each time. It should only be done once on power up.*/
//    cs4224_hard_reset(slice);

    /* The cs4224_rules_set_default() method is used to default the rules */
    /* for a particular target application. They can be customized before */
    /* calling cs4224_slice_enter_operational_state() */
    /* Data rate = 10754.60325 MHz */
    /* Clock rate = 156.25 MHz */
    cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		rules.ref_clk_rate                     = 107.5460325;//CS4224_REF_CLK_106p25;
		rules.fracdiv.enable                   = FALSE; 
	  rules.clkdiv.enable = TRUE;
		rules.clkdiv.fastdiv = 0x5; /* div by 66 */ 
		rules.clkdiv.ddiv    = CS4224_DDIV_DIV1;
		rules.clkdiv.rdiv    = CS4224_RDIV_DIV100;  
    /* Enable the fractional divider since 10754.60325/64 != 156.25 */
    /*   divisor   = floor(vco_rate/(8.0*ref_clk)) */
    /*             = floor(10754.60325/(8.0*156.25) */
    /*   numerator = round(pow(2.0,24)*((vco_rate/(8*ref_clk))-divisor,6) */
    /*             = round(16777216.000000 * (8.603683 - 8),6) */
    /*             = round(10128113.375642,6) */
    /* Double checking the calculation: */
    /*             = 8 * ref_clk * (divisor + (numerator/0xffffff)) */
    /*             = 8 * 156.250000 * (divisor + (0.603683) */
    /*             = 8 * 156.250000 * (8.603683) */
    /*             = 10754.603267 */
//    rules.fracdiv.enable    = TRUE;
//    rules.fracdiv.numerator = 0x9a8af1;
//    rules.fracdiv.divisor   = 0x8;

    /* Configuring the microcode image */
    rules.rx_if.dplx_line_edc_mode = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_edc_mode = CS_HSIO_EDC_MODE_SR;

    /* Configuring the trace loss settings */
    /* The driver trace loss may need further tuning for the target application */
    /* The equalizer trace loss is currently only used in SR mode or when the */
    /* microcode is disabled */
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;


    for(slice = 0; slice < CS4224_MAX_NUM_SLICES(0); slice=slice+2)
    {
        status |= cs4224_slice_enter_operational_state(slice, &rules);

    }

    return status;
}

cs_status cs4343_configure_otu3_inv()
{
    cs_status status = CS_OK; /* return code, CS_OK == good
                                              CS_ERROR == bad*/
    cs_uint32 slice = 0;      /* The slice/channel being configured*/
    cs4224_rules_t rules;     /* The configuration rules for initializing
                                 the device */
    e_cs4224_polarity_inv_t slice_inv[] = {  \
        /*0*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_HOST_TX) , \
        /*1*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*3*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*5*/ CS4224_POLARITY_INV_NONE , \
        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_RX, \
        /*7*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_RX | CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
    };

    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. The slice parameter is only
       passed so that the upper 24 bits can be used to reference the
       appropriate ASIC. The hard reset is necessary to properly
       initialize all registers since some are not clocked by default
       when the external reset pin is toggled. Do not call this method
       multiple times each time you configure a slice as it will reset
       the whole ASIC each time. It should only be done once on power up.*/
//    cs4224_hard_reset(slice);

    /* The cs4224_rules_set_default() method is used to default the rules */
    /* for a particular target application. They can be customized before */
    /* calling cs4224_slice_enter_operational_state() */
    /* Data rate = 10754.60325 MHz */
    /* Clock rate = 156.25 MHz */
    cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		rules.ref_clk_rate                     = 107.5460325;//CS4224_REF_CLK_106p25;
		rules.fracdiv.enable                   = FALSE; 
	  rules.clkdiv.enable = TRUE;
		rules.clkdiv.fastdiv = 0x5; /* div by 66 */ 
		rules.clkdiv.ddiv    = CS4224_DDIV_DIV1;
		rules.clkdiv.rdiv    = CS4224_RDIV_DIV100;  
    /* Enable the fractional divider since 10754.60325/64 != 156.25 */
    /*   divisor   = floor(vco_rate/(8.0*ref_clk)) */
    /*             = floor(10754.60325/(8.0*156.25) */
    /*   numerator = round(pow(2.0,24)*((vco_rate/(8*ref_clk))-divisor,6) */
    /*             = round(16777216.000000 * (8.603683 - 8),6) */
    /*             = round(10128113.375642,6) */
    /* Double checking the calculation: */
    /*             = 8 * ref_clk * (divisor + (numerator/0xffffff)) */
    /*             = 8 * 156.250000 * (divisor + (0.603683) */
    /*             = 8 * 156.250000 * (8.603683) */
    /*             = 10754.603267 */
//    rules.fracdiv.enable    = TRUE;
//    rules.fracdiv.numerator = 0x9a8af1;
//    rules.fracdiv.divisor   = 0x8;

    /* Configuring the microcode image */
    rules.rx_if.dplx_line_edc_mode = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_edc_mode = CS_HSIO_EDC_MODE_SR;

    /* Configuring the trace loss settings */
    /* The driver trace loss may need further tuning for the target application */
    /* The equalizer trace loss is currently only used in SR mode or when the */
    /* microcode is disabled */
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
		
		rules.enable_fec   = TRUE;

    for(slice = 0; slice < CS4224_MAX_NUM_SLICES(0); slice=slice+2)
    {
      rules.polarity_inv = slice_inv[slice];  
			status |= cs4224_slice_enter_operational_state(slice, &rules);
    }

    return status;
}

/* Configuration script for OTU3e2   (11145.83875Mbps)     traffic */
cs_status cs4343_configure_otu3e2()
{
    cs_status status = CS_OK; /* return code, CS_OK == good
                                              CS_ERROR == bad*/
    cs_uint32 slice = 0;      /* The slice/channel being configured*/
    cs4224_rules_t rules;     /* The configuration rules for initializing
                                 the device */


    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. The slice parameter is only
       passed so that the upper 24 bits can be used to reference the
       appropriate ASIC. The hard reset is necessary to properly
       initialize all registers since some are not clocked by default
       when the external reset pin is toggled. Do not call this method
       multiple times each time you configure a slice as it will reset
       the whole ASIC each time. It should only be done once on power up.*/
//    cs4224_hard_reset(slice);

    /* The cs4224_rules_set_default() method is used to default the rules */
    /* for a particular target application. They can be customized before */
    /* calling cs4224_slice_enter_operational_state() */
    /* Data rate = 11145.83875 MHz */
    /* Clock rate = 156.25 MHz */
    cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		rules.ref_clk_rate                     = 111.4583875;//CS4224_REF_CLK_106p25;
		rules.fracdiv.enable                   = FALSE; 
	  rules.clkdiv.enable = TRUE;
		rules.clkdiv.fastdiv = 0x5; /* div by 66 */ 
		rules.clkdiv.ddiv    = CS4224_DDIV_DIV1;
		rules.clkdiv.rdiv    = CS4224_RDIV_DIV100;  
    /* Enable the fractional divider since 11145.83875/64 != 156.25 */
    /*   divisor   = floor(vco_rate/(8.0*ref_clk)) */
    /*             = floor(11145.83875/(8.0*156.25) */
    /*   numerator = round(pow(2.0,24)*((vco_rate/(8*ref_clk))-divisor,6) */
    /*             = round(16777216.000000 * (8.916671 - 8),6) */
    /*             = round(15379187.367936,6) */
    /* Double checking the calculation: */
    /*             = 8 * ref_clk * (divisor + (numerator/0xffffff)) */
    /*             = 8 * 156.250000 * (divisor + (0.916671) */
    /*             = 8 * 156.250000 * (8.916671) */
    /*             = 11145.838791 */
//    rules.fracdiv.enable    = TRUE;
//    rules.fracdiv.numerator = 0xeaaaf3;
//    rules.fracdiv.divisor   = 0x8;

    /* Configuring the microcode image */
    rules.rx_if.dplx_line_edc_mode = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_edc_mode = CS_HSIO_EDC_MODE_SR;

    /* Configuring the trace loss settings */
    /* The driver trace loss may need further tuning for the target application */
    /* The equalizer trace loss is currently only used in SR mode or when the */
    /* microcode is disabled */
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;


    for(slice = 0; slice < CS4224_MAX_NUM_SLICES(0); slice=slice+2)
    {
        status |= cs4224_slice_enter_operational_state(slice, &rules);

    }

    return status;
}

cs_status cs4343_configure_otu3e2_inv()
{
    cs_status status = CS_OK; /* return code, CS_OK == good
                                              CS_ERROR == bad*/
    cs_uint32 slice = 0;      /* The slice/channel being configured*/
    cs4224_rules_t rules;     /* The configuration rules for initializing
                                 the device */
    e_cs4224_polarity_inv_t slice_inv[] = {  \
        /*0*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_HOST_TX) , \
        /*1*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*3*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*5*/ CS4224_POLARITY_INV_NONE , \
        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_RX, \
        /*7*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_RX | CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
    };

    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. The slice parameter is only
       passed so that the upper 24 bits can be used to reference the
       appropriate ASIC. The hard reset is necessary to properly
       initialize all registers since some are not clocked by default
       when the external reset pin is toggled. Do not call this method
       multiple times each time you configure a slice as it will reset
       the whole ASIC each time. It should only be done once on power up.*/
//    cs4224_hard_reset(slice);

    /* The cs4224_rules_set_default() method is used to default the rules */
    /* for a particular target application. They can be customized before */
    /* calling cs4224_slice_enter_operational_state() */
    /* Data rate = 11145.83875 MHz */
    /* Clock rate = 156.25 MHz */
    cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		rules.ref_clk_rate                     = 111.4583875;//CS4224_REF_CLK_106p25;
		rules.fracdiv.enable                   = FALSE; 
	  rules.clkdiv.enable = TRUE;
		rules.clkdiv.fastdiv = 0x5; /* div by 66 */ 
		rules.clkdiv.ddiv    = CS4224_DDIV_DIV1;
		rules.clkdiv.rdiv    = CS4224_RDIV_DIV100;  
    /* Enable the fractional divider since 11145.83875/64 != 156.25 */
    /*   divisor   = floor(vco_rate/(8.0*ref_clk)) */
    /*             = floor(11145.83875/(8.0*156.25) */
    /*   numerator = round(pow(2.0,24)*((vco_rate/(8*ref_clk))-divisor,6) */
    /*             = round(16777216.000000 * (8.916671 - 8),6) */
    /*             = round(15379187.367936,6) */
    /* Double checking the calculation: */
    /*             = 8 * ref_clk * (divisor + (numerator/0xffffff)) */
    /*             = 8 * 156.250000 * (divisor + (0.916671) */
    /*             = 8 * 156.250000 * (8.916671) */
    /*             = 11145.838791 */
//    rules.fracdiv.enable    = TRUE;
//    rules.fracdiv.numerator = 0xeaaaf3;
//    rules.fracdiv.divisor   = 0x8;

    /* Configuring the microcode image */
    rules.rx_if.dplx_line_edc_mode = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_edc_mode = CS_HSIO_EDC_MODE_SR;

    /* Configuring the trace loss settings */
    /* The driver trace loss may need further tuning for the target application */
    /* The equalizer trace loss is currently only used in SR mode or when the */
    /* microcode is disabled */
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
		
		rules.enable_fec   = TRUE;

    for(slice = 0; slice < CS4224_MAX_NUM_SLICES(0); slice=slice+2)
    {
      rules.polarity_inv = slice_inv[slice];  
			status |= cs4224_slice_enter_operational_state(slice, &rules);
    }

    return status;
}

/* Configuration script for CPRI8   (10137.6Mbps)     traffic */
cs_status cs4343_configure_cpri8()
{
    cs_status status = CS_OK; /* return code, CS_OK == good
                                              CS_ERROR == bad*/
    cs_uint32 slice = 0;      /* The slice/channel being configured*/
    cs4224_rules_t rules;     /* The configuration rules for initializing
                                 the device */


    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. The slice parameter is only
       passed so that the upper 24 bits can be used to reference the
       appropriate ASIC. The hard reset is necessary to properly
       initialize all registers since some are not clocked by default
       when the external reset pin is toggled. Do not call this method
       multiple times each time you configure a slice as it will reset
       the whole ASIC each time. It should only be done once on power up.*/
//    cs4224_hard_reset(slice);

    /* The cs4224_rules_set_default() method is used to default the rules */
    /* for a particular target application. They can be customized before */
    /* calling cs4224_slice_enter_operational_state() */
    /* Data rate = 10137.60000 MHz */
    /* Clock rate = 156.25 MHz */
    cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		rules.ref_clk_rate                     = 153.6;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_1;
		rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_66;
    /* Enable the fractional divider since 10137.60000/64 != 156.25 */
    /*   divisor   = floor(vco_rate/(8.0*ref_clk)) */
    /*             = floor(10137.60000/(8.0*156.25) */
    /*   numerator = round(pow(2.0,24)*((vco_rate/(8*ref_clk))-divisor,6) */
    /*             = round(16777216.000000 * (8.110080 - 8),6) */
    /*             = round(1846835.937280,6) */
    /* Double checking the calculation: */
    /*             = 8 * ref_clk * (divisor + (numerator/0xffffff)) */
    /*             = 8 * 156.250000 * (divisor + (0.110080) */
    /*             = 8 * 156.250000 * (8.110080) */
    /*             = 10137.599938 */
//    rules.fracdiv.enable    = TRUE;
//    rules.fracdiv.numerator = 0x1c2e33;
//    rules.fracdiv.divisor   = 0x8;

    /* Configuring the microcode image */
    rules.rx_if.dplx_line_edc_mode = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_edc_mode = CS_HSIO_EDC_MODE_SR;

    /* Configuring the trace loss settings */
    /* The driver trace loss may need further tuning for the target application */
    /* The equalizer trace loss is currently only used in SR mode or when the */
    /* microcode is disabled */
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;


    for(slice = 0; slice < CS4224_MAX_NUM_SLICES(0); slice=slice+2)
    {
        status |= cs4224_slice_enter_operational_state(slice, &rules);

    }

    return status;
}

cs_status cs4343_configure_cpri8_inv()
{
    cs_status status = CS_OK; /* return code, CS_OK == good
                                              CS_ERROR == bad*/
    cs_uint32 slice = 0;      /* The slice/channel being configured*/
    cs4224_rules_t rules;     /* The configuration rules for initializing
                                 the device */
    e_cs4224_polarity_inv_t slice_inv[] = {  \
        /*0*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_HOST_TX) , \
        /*1*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*3*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*5*/ CS4224_POLARITY_INV_NONE , \
        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_RX, \
        /*7*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_RX | CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
    };

    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. The slice parameter is only
       passed so that the upper 24 bits can be used to reference the
       appropriate ASIC. The hard reset is necessary to properly
       initialize all registers since some are not clocked by default
       when the external reset pin is toggled. Do not call this method
       multiple times each time you configure a slice as it will reset
       the whole ASIC each time. It should only be done once on power up.*/
//    cs4224_hard_reset(slice);

    /* The cs4224_rules_set_default() method is used to default the rules */
    /* for a particular target application. They can be customized before */
    /* calling cs4224_slice_enter_operational_state() */
    /* Data rate = 10137.60000 MHz */
    /* Clock rate = 156.25 MHz */
    cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		rules.ref_clk_rate                     = 153.6;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_1;
		rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_66;
    /* Enable the fractional divider since 10137.60000/64 != 156.25 */
    /*   divisor   = floor(vco_rate/(8.0*ref_clk)) */
    /*             = floor(10137.60000/(8.0*156.25) */
    /*   numerator = round(pow(2.0,24)*((vco_rate/(8*ref_clk))-divisor,6) */
    /*             = round(16777216.000000 * (8.110080 - 8),6) */
    /*             = round(1846835.937280,6) */
    /* Double checking the calculation: */
    /*             = 8 * ref_clk * (divisor + (numerator/0xffffff)) */
    /*             = 8 * 156.250000 * (divisor + (0.110080) */
    /*             = 8 * 156.250000 * (8.110080) */
    /*             = 10137.599938 */
//    rules.fracdiv.enable    = TRUE;
//    rules.fracdiv.numerator = 0x1c2e33;
//    rules.fracdiv.divisor   = 0x8;

    /* Configuring the microcode image */
    rules.rx_if.dplx_line_edc_mode = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_edc_mode = CS_HSIO_EDC_MODE_SR;

    /* Configuring the trace loss settings */
    /* The driver trace loss may need further tuning for the target application */
    /* The equalizer trace loss is currently only used in SR mode or when the */
    /* microcode is disabled */
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
				
		rules.enable_fec   = TRUE;

    for(slice = 0; slice < CS4224_MAX_NUM_SLICES(0); slice=slice+2)
    {
      rules.polarity_inv = slice_inv[slice];  
			status |= cs4224_slice_enter_operational_state(slice, &rules);
    }

    return status;
}

/* Configuration script for CPRI7    (9830.4Mbps)     traffic */
cs_status cs4343_configure_cpri7()
{
    cs_status status = CS_OK; /* return code, CS_OK == good
                                              CS_ERROR == bad*/
    cs_uint32 slice = 0;      /* The slice/channel being configured*/
    cs4224_rules_t rules;     /* The configuration rules for initializing
                                 the device */


    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. The slice parameter is only
       passed so that the upper 24 bits can be used to reference the
       appropriate ASIC. The hard reset is necessary to properly
       initialize all registers since some are not clocked by default
       when the external reset pin is toggled. Do not call this method
       multiple times each time you configure a slice as it will reset
       the whole ASIC each time. It should only be done once on power up.*/
//    cs4224_hard_reset(slice);

    /* The cs4224_rules_set_default() method is used to default the rules */
    /* for a particular target application. They can be customized before */
    /* calling cs4224_slice_enter_operational_state() */
    /* Data rate = 9830.40000 MHz */
    /* Clock rate = 156.25 MHz */
    cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		rules.ref_clk_rate                     = 153.6;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_1;
		rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_64;
    /* Enable the fractional divider since 9830.40000/64 != 156.25 */
    /*   divisor   = floor(vco_rate/(8.0*ref_clk)) */
    /*             = floor(9830.40000/(8.0*156.25) */
    /*   numerator = round(pow(2.0,24)*((vco_rate/(8*ref_clk))-divisor,6) */
    /*             = round(16777216.000000 * (7.864320 - 7),6) */
    /*             = round(14500883.333120,6) */
    /* Double checking the calculation: */
    /*             = 8 * ref_clk * (divisor + (numerator/0xffffff)) */
    /*             = 8 * 156.250000 * (divisor + (0.864320) */
    /*             = 8 * 156.250000 * (7.864320) */
    /*             = 9830.400040 */
//    rules.fracdiv.enable    = TRUE;
//    rules.fracdiv.numerator = 0xdd4413;
//    rules.fracdiv.divisor   = 0x7;

    /* Configuring the microcode image */
    rules.rx_if.dplx_line_edc_mode = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_edc_mode = CS_HSIO_EDC_MODE_SR;

    /* Configuring the trace loss settings */
    /* The driver trace loss may need further tuning for the target application */
    /* The equalizer trace loss is currently only used in SR mode or when the */
    /* microcode is disabled */
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;


    for(slice = 0; slice < CS4224_MAX_NUM_SLICES(0); slice=slice+2)
    {
        status |= cs4224_slice_enter_operational_state(slice, &rules);

    }

    return status;
}

cs_status cs4343_configure_cpri7_inv()
{
    cs_status status = CS_OK; /* return code, CS_OK == good
                                              CS_ERROR == bad*/
    cs_uint32 slice = 0;      /* The slice/channel being configured*/
    cs4224_rules_t rules;     /* The configuration rules for initializing
                                 the device */
    e_cs4224_polarity_inv_t slice_inv[] = {  \
        /*0*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_HOST_TX) , \
        /*1*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*3*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*5*/ CS4224_POLARITY_INV_NONE , \
        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_RX, \
        /*7*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_RX | CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
    };

    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. The slice parameter is only
       passed so that the upper 24 bits can be used to reference the
       appropriate ASIC. The hard reset is necessary to properly
       initialize all registers since some are not clocked by default
       when the external reset pin is toggled. Do not call this method
       multiple times each time you configure a slice as it will reset
       the whole ASIC each time. It should only be done once on power up.*/
//    cs4224_hard_reset(slice);

    /* The cs4224_rules_set_default() method is used to default the rules */
    /* for a particular target application. They can be customized before */
    /* calling cs4224_slice_enter_operational_state() */
    /* Data rate = 9830.40000 MHz */
    /* Clock rate = 156.25 MHz */
    cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		rules.ref_clk_rate                     = 153.6;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_1;
		rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_64;
    /* Enable the fractional divider since 9830.40000/64 != 156.25 */
    /*   divisor   = floor(vco_rate/(8.0*ref_clk)) */
    /*             = floor(9830.40000/(8.0*156.25) */
    /*   numerator = round(pow(2.0,24)*((vco_rate/(8*ref_clk))-divisor,6) */
    /*             = round(16777216.000000 * (7.864320 - 7),6) */
    /*             = round(14500883.333120,6) */
    /* Double checking the calculation: */
    /*             = 8 * ref_clk * (divisor + (numerator/0xffffff)) */
    /*             = 8 * 156.250000 * (divisor + (0.864320) */
    /*             = 8 * 156.250000 * (7.864320) */
    /*             = 9830.400040 */
//    rules.fracdiv.enable    = TRUE;
//    rules.fracdiv.numerator = 0xdd4413;
//    rules.fracdiv.divisor   = 0x7;

    /* Configuring the microcode image */
    rules.rx_if.dplx_line_edc_mode = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_edc_mode = CS_HSIO_EDC_MODE_SR;

    /* Configuring the trace loss settings */
    /* The driver trace loss may need further tuning for the target application */
    /* The equalizer trace loss is currently only used in SR mode or when the */
    /* microcode is disabled */
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
		
		rules.enable_fec   = TRUE;


    for(slice = 0; slice < CS4224_MAX_NUM_SLICES(0); slice=slice+2)
    {
      rules.polarity_inv = slice_inv[slice];  
			status |= cs4224_slice_enter_operational_state(slice, &rules);
    }

    return status;
}

/* Configuration script for CPRI6    (6144.0Mbps)     traffic */
cs_status cs4343_configure_cpri6()
{
    cs_status status = CS_OK; /* return code, CS_OK == good
                                              CS_ERROR == bad*/
    cs_uint32 slice = 0;      /* The slice/channel being configured*/
    cs4224_rules_t rules;     /* The configuration rules for initializing
                                 the device */


    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. The slice parameter is only
       passed so that the upper 24 bits can be used to reference the
       appropriate ASIC. The hard reset is necessary to properly
       initialize all registers since some are not clocked by default
       when the external reset pin is toggled. Do not call this method
       multiple times each time you configure a slice as it will reset
       the whole ASIC each time. It should only be done once on power up.*/
//    cs4224_hard_reset(slice);

    /* The cs4224_rules_set_default() method is used to default the rules */
    /* for a particular target application. They can be customized before */
    /* calling cs4224_slice_enter_operational_state() */
    /* Data rate = 6144.00000 MHz */
    /* Clock rate = 156.25 MHz */
    cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		rules.ref_clk_rate                     = 153.6;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_2;
		rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_80;
    /* Enable the fractional divider since 12288.00000/64 != 156.25 */
    /*   divisor   = floor(vco_rate/(8.0*ref_clk)) */
    /*             = floor(12288.00000/(8.0*156.25) */
    /*   numerator = round(pow(2.0,24)*((vco_rate/(8*ref_clk))-divisor,6) */
    /*             = round(16777216.000000 * (9.830400 - 9),6) */
    /*             = round(13931800.166400,6) */
    /* Double checking the calculation: */
    /*             = 8 * ref_clk * (divisor + (numerator/0xffffff)) */
    /*             = 8 * 156.250000 * (divisor + (0.830400) */
    /*             = 8 * 156.250000 * (9.830400) */
    /*             = 12288.000049 */
//    rules.fracdiv.enable    = TRUE;
//    rules.fracdiv.numerator = 0xd49518;
//    rules.fracdiv.divisor   = 0x9;

//    /* Configure the RDIV/DDIV parameters */
//    rules.clkdiv.enable = TRUE;
//    rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_2;

    /* Configuring the microcode image */
    rules.rx_if.dplx_line_edc_mode = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_edc_mode = CS_HSIO_EDC_MODE_SR;

    /* Configuring the trace loss settings */
    /* The driver trace loss may need further tuning for the target application */
    /* The equalizer trace loss is currently only used in SR mode or when the */
    /* microcode is disabled */
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;


    for(slice = 0; slice < CS4224_MAX_NUM_SLICES(0); slice=slice+2)
    {
        status |= cs4224_slice_enter_operational_state(slice, &rules);

    }

    return status;
}


cs_status cs4343_configure_cpri6_inv()
{
    cs_status status = CS_OK; /* return code, CS_OK == good
                                              CS_ERROR == bad*/
    cs_uint32 slice = 0;      /* The slice/channel being configured*/
    cs4224_rules_t rules;     /* The configuration rules for initializing
                                 the device */
    e_cs4224_polarity_inv_t slice_inv[] = {  \
        /*0*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_HOST_TX) , \
        /*1*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*3*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*5*/ CS4224_POLARITY_INV_NONE , \
        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_RX, \
        /*7*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_RX | CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
    };

    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. The slice parameter is only
       passed so that the upper 24 bits can be used to reference the
       appropriate ASIC. The hard reset is necessary to properly
       initialize all registers since some are not clocked by default
       when the external reset pin is toggled. Do not call this method
       multiple times each time you configure a slice as it will reset
       the whole ASIC each time. It should only be done once on power up.*/
//    cs4224_hard_reset(slice);

    /* The cs4224_rules_set_default() method is used to default the rules */
    /* for a particular target application. They can be customized before */
    /* calling cs4224_slice_enter_operational_state() */
    /* Data rate = 6144.00000 MHz */
    /* Clock rate = 156.25 MHz */
    cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		rules.ref_clk_rate                     = 153.6;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_2;
		rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_80;
    /* Enable the fractional divider since 12288.00000/64 != 156.25 */
    /*   divisor   = floor(vco_rate/(8.0*ref_clk)) */
    /*             = floor(12288.00000/(8.0*156.25) */
    /*   numerator = round(pow(2.0,24)*((vco_rate/(8*ref_clk))-divisor,6) */
    /*             = round(16777216.000000 * (9.830400 - 9),6) */
    /*             = round(13931800.166400,6) */
    /* Double checking the calculation: */
    /*             = 8 * ref_clk * (divisor + (numerator/0xffffff)) */
    /*             = 8 * 156.250000 * (divisor + (0.830400) */
    /*             = 8 * 156.250000 * (9.830400) */
    /*             = 12288.000049 */
//    rules.fracdiv.enable    = TRUE;
//    rules.fracdiv.numerator = 0xd49518;
//    rules.fracdiv.divisor   = 0x9;

//    /* Configure the RDIV/DDIV parameters */
//    rules.clkdiv.enable = TRUE;
//    rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_2;

    /* Configuring the microcode image */
    rules.rx_if.dplx_line_edc_mode = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_edc_mode = CS_HSIO_EDC_MODE_SR;

    /* Configuring the trace loss settings */
    /* The driver trace loss may need further tuning for the target application */
    /* The equalizer trace loss is currently only used in SR mode or when the */
    /* microcode is disabled */
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
		
		rules.enable_fec   = TRUE;

    for(slice = 0; slice < CS4224_MAX_NUM_SLICES(0); slice=slice+2)
    {
      rules.polarity_inv = slice_inv[slice];  
			status |= cs4224_slice_enter_operational_state(slice, &rules);
    }

    return status;
}

/* Configuration script for CPRI5    (4915.2Mbps)     traffic */
cs_status cs4343_configure_cpri5()
{
    cs_status status = CS_OK; /* return code, CS_OK == good
                                              CS_ERROR == bad*/
    cs_uint32 slice = 0;      /* The slice/channel being configured*/
    cs4224_rules_t rules;     /* The configuration rules for initializing
                                 the device */


    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. The slice parameter is only
       passed so that the upper 24 bits can be used to reference the
       appropriate ASIC. The hard reset is necessary to properly
       initialize all registers since some are not clocked by default
       when the external reset pin is toggled. Do not call this method
       multiple times each time you configure a slice as it will reset
       the whole ASIC each time. It should only be done once on power up.*/
//    cs4224_hard_reset(slice);

    /* The cs4224_rules_set_default() method is used to default the rules */
    /* for a particular target application. They can be customized before */
    /* calling cs4224_slice_enter_operational_state() */
    /* Data rate = 4915.20000 MHz */
    /* Clock rate = 156.25 MHz */
    cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		rules.ref_clk_rate                     = 153.6;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_2;
		rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_64;
    /* Enable the fractional divider since 9830.40000/64 != 156.25 */
    /*   divisor   = floor(vco_rate/(8.0*ref_clk)) */
    /*             = floor(9830.40000/(8.0*156.25) */
    /*   numerator = round(pow(2.0,24)*((vco_rate/(8*ref_clk))-divisor,6) */
    /*             = round(16777216.000000 * (7.864320 - 7),6) */
    /*             = round(14500883.333120,6) */
    /* Double checking the calculation: */
    /*             = 8 * ref_clk * (divisor + (numerator/0xffffff)) */
    /*             = 8 * 156.250000 * (divisor + (0.864320) */
    /*             = 8 * 156.250000 * (7.864320) */
    /*             = 9830.400040 */
//    rules.fracdiv.enable    = TRUE;
//    rules.fracdiv.numerator = 0xdd4413;
//    rules.fracdiv.divisor   = 0x7;

//    /* Configure the RDIV/DDIV parameters */
//    rules.clkdiv.enable = TRUE;
//    rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_2;

    /* Configuring the microcode image */
    rules.rx_if.dplx_line_edc_mode = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_edc_mode = CS_HSIO_EDC_MODE_SR;

    /* Configuring the trace loss settings */
    /* The driver trace loss may need further tuning for the target application */
    /* The equalizer trace loss is currently only used in SR mode or when the */
    /* microcode is disabled */
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;


    for(slice = 0; slice < CS4224_MAX_NUM_SLICES(0); slice=slice+2)
    {
        status |= cs4224_slice_enter_operational_state(slice, &rules);

    }

    return status;
}


cs_status cs4343_configure_cpri5_inv()
{
    cs_status status = CS_OK; /* return code, CS_OK == good
                                              CS_ERROR == bad*/
    cs_uint32 slice = 0;      /* The slice/channel being configured*/
    cs4224_rules_t rules;     /* The configuration rules for initializing
                                 the device */
    e_cs4224_polarity_inv_t slice_inv[] = {  \
        /*0*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_HOST_TX) , \
        /*1*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*3*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*5*/ CS4224_POLARITY_INV_NONE , \
        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_RX, \
        /*7*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_RX | CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
    };

    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. The slice parameter is only
       passed so that the upper 24 bits can be used to reference the
       appropriate ASIC. The hard reset is necessary to properly
       initialize all registers since some are not clocked by default
       when the external reset pin is toggled. Do not call this method
       multiple times each time you configure a slice as it will reset
       the whole ASIC each time. It should only be done once on power up.*/
//    cs4224_hard_reset(slice);

    /* The cs4224_rules_set_default() method is used to default the rules */
    /* for a particular target application. They can be customized before */
    /* calling cs4224_slice_enter_operational_state() */
    /* Data rate = 4915.20000 MHz */
    /* Clock rate = 156.25 MHz */
    cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		rules.ref_clk_rate                     = 153.6;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_2;
		rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_64;
    /* Enable the fractional divider since 9830.40000/64 != 156.25 */
    /*   divisor   = floor(vco_rate/(8.0*ref_clk)) */
    /*             = floor(9830.40000/(8.0*156.25) */
    /*   numerator = round(pow(2.0,24)*((vco_rate/(8*ref_clk))-divisor,6) */
    /*             = round(16777216.000000 * (7.864320 - 7),6) */
    /*             = round(14500883.333120,6) */
    /* Double checking the calculation: */
    /*             = 8 * ref_clk * (divisor + (numerator/0xffffff)) */
    /*             = 8 * 156.250000 * (divisor + (0.864320) */
    /*             = 8 * 156.250000 * (7.864320) */
    /*             = 9830.400040 */
//    rules.fracdiv.enable    = TRUE;
//    rules.fracdiv.numerator = 0xdd4413;
//    rules.fracdiv.divisor   = 0x7;

//    /* Configure the RDIV/DDIV parameters */
//    rules.clkdiv.enable = TRUE;
//    rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_2;

    /* Configuring the microcode image */
    rules.rx_if.dplx_line_edc_mode = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_edc_mode = CS_HSIO_EDC_MODE_SR;

    /* Configuring the trace loss settings */
    /* The driver trace loss may need further tuning for the target application */
    /* The equalizer trace loss is currently only used in SR mode or when the */
    /* microcode is disabled */
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
		
		rules.enable_fec   = TRUE;

    for(slice = 0; slice < CS4224_MAX_NUM_SLICES(0); slice=slice+2)
    {
      rules.polarity_inv = slice_inv[slice];  
			status |= cs4224_slice_enter_operational_state(slice, &rules);
    }

    return status;
}

/* Configuration script for CPRI4    (3072.0Mbps)     traffic */
cs_status cs4343_configure_cpri4()
{
    cs_status status = CS_OK; /* return code, CS_OK == good
                                              CS_ERROR == bad*/
    cs_uint32 slice = 0;      /* The slice/channel being configured*/
    cs4224_rules_t rules;     /* The configuration rules for initializing
                                 the device */


    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. The slice parameter is only
       passed so that the upper 24 bits can be used to reference the
       appropriate ASIC. The hard reset is necessary to properly
       initialize all registers since some are not clocked by default
       when the external reset pin is toggled. Do not call this method
       multiple times each time you configure a slice as it will reset
       the whole ASIC each time. It should only be done once on power up.*/
//    cs4224_hard_reset(slice);

    /* The cs4224_rules_set_default() method is used to default the rules */
    /* for a particular target application. They can be customized before */
    /* calling cs4224_slice_enter_operational_state() */
    /* Data rate = 3072.00000 MHz */
    /* Clock rate = 156.25 MHz */
    cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		rules.ref_clk_rate                     = 153.6;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_4;
		rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_80;
    /* Enable the fractional divider since 12288.00000/64 != 156.25 */
    /*   divisor   = floor(vco_rate/(8.0*ref_clk)) */
    /*             = floor(12288.00000/(8.0*156.25) */
    /*   numerator = round(pow(2.0,24)*((vco_rate/(8*ref_clk))-divisor,6) */
    /*             = round(16777216.000000 * (9.830400 - 9),6) */
    /*             = round(13931800.166400,6) */
    /* Double checking the calculation: */
    /*             = 8 * ref_clk * (divisor + (numerator/0xffffff)) */
    /*             = 8 * 156.250000 * (divisor + (0.830400) */
    /*             = 8 * 156.250000 * (9.830400) */
    /*             = 12288.000049 */
//    rules.fracdiv.enable    = TRUE;
//    rules.fracdiv.numerator = 0xd49518;
//    rules.fracdiv.divisor   = 0x9;

//    /* Configure the RDIV/DDIV parameters */
//    rules.clkdiv.enable = TRUE;
//    rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_4;

    /* Configuring the microcode image */
    rules.rx_if.dplx_line_edc_mode = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_edc_mode = CS_HSIO_EDC_MODE_SR;

    /* Configuring the trace loss settings */
    /* The driver trace loss may need further tuning for the target application */
    /* The equalizer trace loss is currently only used in SR mode or when the */
    /* microcode is disabled */
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;


    for(slice = 0; slice < CS4224_MAX_NUM_SLICES(0); slice=slice+2)
    {
        status |= cs4224_slice_enter_operational_state(slice, &rules);

    }

    return status;
}

cs_status cs4343_configure_cpri4_inv()
{
    cs_status status = CS_OK; /* return code, CS_OK == good
                                              CS_ERROR == bad*/
    cs_uint32 slice = 0;      /* The slice/channel being configured*/
    cs4224_rules_t rules;     /* The configuration rules for initializing
                                 the device */
    e_cs4224_polarity_inv_t slice_inv[] = {  \
       /*0*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_HOST_TX) , \
        /*1*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*3*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*5*/ CS4224_POLARITY_INV_NONE , \
        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_RX, \
        /*7*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_RX | CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
    };

    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. The slice parameter is only
       passed so that the upper 24 bits can be used to reference the
       appropriate ASIC. The hard reset is necessary to properly
       initialize all registers since some are not clocked by default
       when the external reset pin is toggled. Do not call this method
       multiple times each time you configure a slice as it will reset
       the whole ASIC each time. It should only be done once on power up.*/
//    cs4224_hard_reset(slice);

    /* The cs4224_rules_set_default() method is used to default the rules */
    /* for a particular target application. They can be customized before */
    /* calling cs4224_slice_enter_operational_state() */
    /* Data rate = 3072.00000 MHz */
    /* Clock rate = 156.25 MHz */
    cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		rules.ref_clk_rate                     = 153.6;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_4;
		rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_80;
    /* Enable the fractional divider since 12288.00000/64 != 156.25 */
    /*   divisor   = floor(vco_rate/(8.0*ref_clk)) */
    /*             = floor(12288.00000/(8.0*156.25) */
    /*   numerator = round(pow(2.0,24)*((vco_rate/(8*ref_clk))-divisor,6) */
    /*             = round(16777216.000000 * (9.830400 - 9),6) */
    /*             = round(13931800.166400,6) */
    /* Double checking the calculation: */
    /*             = 8 * ref_clk * (divisor + (numerator/0xffffff)) */
    /*             = 8 * 156.250000 * (divisor + (0.830400) */
    /*             = 8 * 156.250000 * (9.830400) */
    /*             = 12288.000049 */
//    rules.fracdiv.enable    = TRUE;
//    rules.fracdiv.numerator = 0xd49518;
//    rules.fracdiv.divisor   = 0x9;

//    /* Configure the RDIV/DDIV parameters */
//    rules.clkdiv.enable = TRUE;
//    rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_4;

    /* Configuring the microcode image */
    rules.rx_if.dplx_line_edc_mode = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_edc_mode = CS_HSIO_EDC_MODE_SR;

    /* Configuring the trace loss settings */
    /* The driver trace loss may need further tuning for the target application */
    /* The equalizer trace loss is currently only used in SR mode or when the */
    /* microcode is disabled */
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
		
		rules.enable_fec   = TRUE;

    for(slice = 0; slice < CS4224_MAX_NUM_SLICES(0); slice=slice+2)
    {
      rules.polarity_inv = slice_inv[slice];  
			status |= cs4224_slice_enter_operational_state(slice, &rules);
    }

    return status;
}

/* Configuration script for CPRI3    (2457.6Mbps)     traffic */
cs_status cs4343_configure_cpri3()
{
    cs_status status = CS_OK; /* return code, CS_OK == good
                                              CS_ERROR == bad*/
    cs_uint32 slice = 0;      /* The slice/channel being configured*/
    cs4224_rules_t rules;     /* The configuration rules for initializing
                                 the device */


    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. The slice parameter is only
       passed so that the upper 24 bits can be used to reference the
       appropriate ASIC. The hard reset is necessary to properly
       initialize all registers since some are not clocked by default
       when the external reset pin is toggled. Do not call this method
       multiple times each time you configure a slice as it will reset
       the whole ASIC each time. It should only be done once on power up.*/
//    cs4224_hard_reset(slice);

    /* The cs4224_rules_set_default() method is used to default the rules */
    /* for a particular target application. They can be customized before */
    /* calling cs4224_slice_enter_operational_state() */
    /* Data rate = 2457.60000 MHz */
    /* Clock rate = 156.25 MHz */
    cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		rules.ref_clk_rate                     = 153.6;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_4;
		rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_64;
    /* Enable the fractional divider since 9830.40000/64 != 156.25 */
    /*   divisor   = floor(vco_rate/(8.0*ref_clk)) */
    /*             = floor(9830.40000/(8.0*156.25) */
    /*   numerator = round(pow(2.0,24)*((vco_rate/(8*ref_clk))-divisor,6) */
    /*             = round(16777216.000000 * (7.864320 - 7),6) */
    /*             = round(14500883.333120,6) */
    /* Double checking the calculation: */
    /*             = 8 * ref_clk * (divisor + (numerator/0xffffff)) */
    /*             = 8 * 156.250000 * (divisor + (0.864320) */
    /*             = 8 * 156.250000 * (7.864320) */
    /*             = 9830.400040 */
//    rules.fracdiv.enable    = TRUE;
//    rules.fracdiv.numerator = 0xdd4413;
//    rules.fracdiv.divisor   = 0x7;

//    /* Configure the RDIV/DDIV parameters */
//    rules.clkdiv.enable = TRUE;
    rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_4;

    /* Configuring the microcode image */
    rules.rx_if.dplx_line_edc_mode = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_edc_mode = CS_HSIO_EDC_MODE_SR;

    /* Configuring the trace loss settings */
    /* The driver trace loss may need further tuning for the target application */
    /* The equalizer trace loss is currently only used in SR mode or when the */
    /* microcode is disabled */
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;


    for(slice = 0; slice < CS4224_MAX_NUM_SLICES(0); slice=slice+2)
    {
        status |= cs4224_slice_enter_operational_state(slice, &rules);

    }

    return status;
}


cs_status cs4343_configure_cpri3_inv()
{
    cs_status status = CS_OK; /* return code, CS_OK == good
                                              CS_ERROR == bad*/
    cs_uint32 slice = 0;      /* The slice/channel being configured*/
    cs4224_rules_t rules;     /* The configuration rules for initializing
                                 the device */
    e_cs4224_polarity_inv_t slice_inv[] = {  \
        /*0*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_HOST_TX) , \
        /*1*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*3*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*5*/ CS4224_POLARITY_INV_NONE , \
        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_RX, \
        /*7*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_RX | CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
    };

    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. The slice parameter is only
       passed so that the upper 24 bits can be used to reference the
       appropriate ASIC. The hard reset is necessary to properly
       initialize all registers since some are not clocked by default
       when the external reset pin is toggled. Do not call this method
       multiple times each time you configure a slice as it will reset
       the whole ASIC each time. It should only be done once on power up.*/
//    cs4224_hard_reset(slice);

    /* The cs4224_rules_set_default() method is used to default the rules */
    /* for a particular target application. They can be customized before */
    /* calling cs4224_slice_enter_operational_state() */
    /* Data rate = 2457.60000 MHz */
    /* Clock rate = 156.25 MHz */
    cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		rules.ref_clk_rate                     = 153.6;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_4;
		rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_64;
    /* Enable the fractional divider since 9830.40000/64 != 156.25 */
    /*   divisor   = floor(vco_rate/(8.0*ref_clk)) */
    /*             = floor(9830.40000/(8.0*156.25) */
    /*   numerator = round(pow(2.0,24)*((vco_rate/(8*ref_clk))-divisor,6) */
    /*             = round(16777216.000000 * (7.864320 - 7),6) */
    /*             = round(14500883.333120,6) */
    /* Double checking the calculation: */
    /*             = 8 * ref_clk * (divisor + (numerator/0xffffff)) */
    /*             = 8 * 156.250000 * (divisor + (0.864320) */
    /*             = 8 * 156.250000 * (7.864320) */
    /*             = 9830.400040 */
//    rules.fracdiv.enable    = TRUE;
//    rules.fracdiv.numerator = 0xdd4413;
//    rules.fracdiv.divisor   = 0x7;

//    /* Configure the RDIV/DDIV parameters */
//    rules.clkdiv.enable = TRUE;
    rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_4;

    /* Configuring the microcode image */
    rules.rx_if.dplx_line_edc_mode = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_edc_mode = CS_HSIO_EDC_MODE_SR;

    /* Configuring the trace loss settings */
    /* The driver trace loss may need further tuning for the target application */
    /* The equalizer trace loss is currently only used in SR mode or when the */
    /* microcode is disabled */
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
		
		rules.enable_fec   = TRUE;

    for(slice = 0; slice < CS4224_MAX_NUM_SLICES(0); slice=slice+2)
    {
      rules.polarity_inv = slice_inv[slice];  
			status |= cs4224_slice_enter_operational_state(slice, &rules);
    }

    return status;
}

/* Configuration script for CPRI2    (1228.8Mbps)     traffic */
cs_status cs4343_configure_cpri2()
{
    cs_status status = CS_OK; /* return code, CS_OK == good
                                              CS_ERROR == bad*/
    cs_uint32 slice = 0;      /* The slice/channel being configured*/
    cs4224_rules_t rules;     /* The configuration rules for initializing
                                 the device */


    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. The slice parameter is only
       passed so that the upper 24 bits can be used to reference the
       appropriate ASIC. The hard reset is necessary to properly
       initialize all registers since some are not clocked by default
       when the external reset pin is toggled. Do not call this method
       multiple times each time you configure a slice as it will reset
       the whole ASIC each time. It should only be done once on power up.*/
//    cs4224_hard_reset(slice);

    /* The cs4224_rules_set_default() method is used to default the rules */
    /* for a particular target application. They can be customized before */
    /* calling cs4224_slice_enter_operational_state() */
    /* Data rate = 1228.80000 MHz */
    /* Clock rate = 156.25 MHz */
    cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		rules.ref_clk_rate                     = 153.6;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_8;
		rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_64;
    /* Enable the fractional divider since 9830.40000/64 != 156.25 */
    /*   divisor   = floor(vco_rate/(8.0*ref_clk)) */
    /*             = floor(9830.40000/(8.0*156.25) */
    /*   numerator = round(pow(2.0,24)*((vco_rate/(8*ref_clk))-divisor,6) */
    /*             = round(16777216.000000 * (7.864320 - 7),6) */
    /*             = round(14500883.333120,6) */
    /* Double checking the calculation: */
    /*             = 8 * ref_clk * (divisor + (numerator/0xffffff)) */
    /*             = 8 * 156.250000 * (divisor + (0.864320) */
    /*             = 8 * 156.250000 * (7.864320) */
    /*             = 9830.400040 */
//    rules.fracdiv.enable    = TRUE;
//    rules.fracdiv.numerator = 0xdd4413;
//    rules.fracdiv.divisor   = 0x7;

//    /* Configure the RDIV/DDIV parameters */
//    rules.clkdiv.enable = TRUE;
//    rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_8;

    /* Configuring the microcode image */
    rules.rx_if.dplx_line_edc_mode = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_edc_mode = CS_HSIO_EDC_MODE_SR;

    /* Configuring the trace loss settings */
    /* The driver trace loss may need further tuning for the target application */
    /* The equalizer trace loss is currently only used in SR mode or when the */
    /* microcode is disabled */
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;


    for(slice = 0; slice < CS4224_MAX_NUM_SLICES(0); slice=slice+2)
    {
        status |= cs4224_slice_enter_operational_state(slice, &rules);

    }

    return status;
}

cs_status cs4343_configure_cpri2_inv()
{
    cs_status status = CS_OK; /* return code, CS_OK == good
                                              CS_ERROR == bad*/
    cs_uint32 slice = 0;      /* The slice/channel being configured*/
    cs4224_rules_t rules;     /* The configuration rules for initializing
                                 the device */
    e_cs4224_polarity_inv_t slice_inv[] = {  \
        /*0*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_HOST_TX) , \
        /*1*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*3*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*5*/ CS4224_POLARITY_INV_NONE , \
        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_RX, \
        /*7*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_RX | CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
    };

    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. The slice parameter is only
       passed so that the upper 24 bits can be used to reference the
       appropriate ASIC. The hard reset is necessary to properly
       initialize all registers since some are not clocked by default
       when the external reset pin is toggled. Do not call this method
       multiple times each time you configure a slice as it will reset
       the whole ASIC each time. It should only be done once on power up.*/
//    cs4224_hard_reset(slice);

    /* The cs4224_rules_set_default() method is used to default the rules */
    /* for a particular target application. They can be customized before */
    /* calling cs4224_slice_enter_operational_state() */
    /* Data rate = 1228.80000 MHz */
    /* Clock rate = 156.25 MHz */
    cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		rules.ref_clk_rate                     = 153.6;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_8;
		rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_64;
    /* Enable the fractional divider since 9830.40000/64 != 156.25 */
    /*   divisor   = floor(vco_rate/(8.0*ref_clk)) */
    /*             = floor(9830.40000/(8.0*156.25) */
    /*   numerator = round(pow(2.0,24)*((vco_rate/(8*ref_clk))-divisor,6) */
    /*             = round(16777216.000000 * (7.864320 - 7),6) */
    /*             = round(14500883.333120,6) */
    /* Double checking the calculation: */
    /*             = 8 * ref_clk * (divisor + (numerator/0xffffff)) */
    /*             = 8 * 156.250000 * (divisor + (0.864320) */
    /*             = 8 * 156.250000 * (7.864320) */
    /*             = 9830.400040 */
//    rules.fracdiv.enable    = TRUE;
//    rules.fracdiv.numerator = 0xdd4413;
//    rules.fracdiv.divisor   = 0x7;

//    /* Configure the RDIV/DDIV parameters */
//    rules.clkdiv.enable = TRUE;
//    rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_8;

    /* Configuring the microcode image */
    rules.rx_if.dplx_line_edc_mode = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_edc_mode = CS_HSIO_EDC_MODE_SR;

    /* Configuring the trace loss settings */
    /* The driver trace loss may need further tuning for the target application */
    /* The equalizer trace loss is currently only used in SR mode or when the */
    /* microcode is disabled */
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
		
		rules.enable_fec   = TRUE;

    for(slice = 0; slice < CS4224_MAX_NUM_SLICES(0); slice=slice+2)
    {
      rules.polarity_inv = slice_inv[slice];  
			status |= cs4224_slice_enter_operational_state(slice, &rules);
    }

    return status;
}

/* Configuration script for CPRI1    (614.4Mbps)     traffic */
cs_status cs4343_configure_cpri1()
{
    cs_status status = CS_OK; /* return code, CS_OK == good
                                              CS_ERROR == bad*/
    cs_uint32 slice = 0;      /* The slice/channel being configured*/
    cs4224_rules_t rules;     /* The configuration rules for initializing
                                 the device */


    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. The slice parameter is only
       passed so that the upper 24 bits can be used to reference the
       appropriate ASIC. The hard reset is necessary to properly
       initialize all registers since some are not clocked by default
       when the external reset pin is toggled. Do not call this method
       multiple times each time you configure a slice as it will reset
       the whole ASIC each time. It should only be done once on power up.*/
//    cs4224_hard_reset(slice);

    /* The cs4224_rules_set_default() method is used to default the rules */
    /* for a particular target application. They can be customized before */
    /* calling cs4224_slice_enter_operational_state() */
    /* Data rate = 1228.80000 MHz */
    /* Clock rate = 156.25 MHz */
    cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		rules.ref_clk_rate                     = 153.6;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_16;
		rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_64;
		

    /* Enable the fractional divider since 9830.40000/64 != 156.25 */
    /*   divisor   = floor(vco_rate/(8.0*ref_clk)) */
    /*             = floor(9830.40000/(8.0*156.25) */
    /*   numerator = round(pow(2.0,24)*((vco_rate/(8*ref_clk))-divisor,6) */
    /*             = round(16777216.000000 * (7.864320 - 7),6) */
    /*             = round(14500883.333120,6) */
    /* Double checking the calculation: */
    /*             = 8 * ref_clk * (divisor + (numerator/0xffffff)) */
    /*             = 8 * 156.250000 * (divisor + (0.864320) */
    /*             = 8 * 156.250000 * (7.864320) */
    /*             = 9830.400040 */
//    rules.fracdiv.enable    = TRUE;
//    rules.fracdiv.numerator = 0xdd4413;
//    rules.fracdiv.divisor   = 0x7;

//    /* Configure the RDIV/DDIV parameters */
//    rules.clkdiv.enable = TRUE;
//    rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_4;
//		rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_64;

    /* Configuring the microcode image */
    rules.rx_if.dplx_line_edc_mode = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_edc_mode = CS_HSIO_EDC_MODE_SR;

    /* Configuring the trace loss settings */
    /* The driver trace loss may need further tuning for the target application */
    /* The equalizer trace loss is currently only used in SR mode or when the */
    /* microcode is disabled */
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;


    for(slice = 0; slice < CS4224_MAX_NUM_SLICES(0); slice=slice+2)
    {
        status |= cs4224_slice_enter_operational_state(slice, &rules);

    }

    return status;
}

cs_status cs4343_configure_cpri1_inv()
{
    cs_status status = CS_OK; /* return code, CS_OK == good
                                              CS_ERROR == bad*/
    cs_uint32 slice = 0;      /* The slice/channel being configured*/
    cs4224_rules_t rules;     /* The configuration rules for initializing
                                 the device */
    e_cs4224_polarity_inv_t slice_inv[] = {  \
        /*0*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_HOST_TX) , \
        /*1*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*2*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*3*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
        /*4*/ CS4224_POLARITY_INV_DPLX_LINE_RX , \
        /*5*/ CS4224_POLARITY_INV_NONE , \
        /*6*/ CS4224_POLARITY_INV_DPLX_LINE_RX, \
        /*7*/ (e_cs4224_polarity_inv_t)(CS4224_POLARITY_INV_DPLX_HOST_RX | CS4224_POLARITY_INV_DPLX_HOST_TX | CS4224_POLARITY_INV_DPLX_LINE_RX | CS4224_POLARITY_INV_DPLX_LINE_TX) , \
    };

    /* First reset the ASIC to ensure it is in operational state.
       This will reset the entire device. The slice parameter is only
       passed so that the upper 24 bits can be used to reference the
       appropriate ASIC. The hard reset is necessary to properly
       initialize all registers since some are not clocked by default
       when the external reset pin is toggled. Do not call this method
       multiple times each time you configure a slice as it will reset
       the whole ASIC each time. It should only be done once on power up.*/
//    cs4224_hard_reset(slice);

    /* The cs4224_rules_set_default() method is used to default the rules */
    /* for a particular target application. They can be customized before */
    /* calling cs4224_slice_enter_operational_state() */
    /* Data rate = 1228.80000 MHz */
    /* Clock rate = 156.25 MHz */
    cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);
		rules.ref_clk_rate                     = 153.6;
		rules.clkdiv.enable = TRUE;
		rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_16;
		rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_64;
		

    /* Enable the fractional divider since 9830.40000/64 != 156.25 */
    /*   divisor   = floor(vco_rate/(8.0*ref_clk)) */
    /*             = floor(9830.40000/(8.0*156.25) */
    /*   numerator = round(pow(2.0,24)*((vco_rate/(8*ref_clk))-divisor,6) */
    /*             = round(16777216.000000 * (7.864320 - 7),6) */
    /*             = round(14500883.333120,6) */
    /* Double checking the calculation: */
    /*             = 8 * ref_clk * (divisor + (numerator/0xffffff)) */
    /*             = 8 * 156.250000 * (divisor + (0.864320) */
    /*             = 8 * 156.250000 * (7.864320) */
    /*             = 9830.400040 */
//    rules.fracdiv.enable    = TRUE;
//    rules.fracdiv.numerator = 0xdd4413;
//    rules.fracdiv.divisor   = 0x7;

//    /* Configure the RDIV/DDIV parameters */
//    rules.clkdiv.enable = TRUE;
//    rules.clkdiv.ddiv = CS4224_CLKDIV_DDIV_BY_4;
//		rules.clkdiv.rdiv = CS4224_CLKDIV_RDIV_BY_64;

    /* Configuring the microcode image */
    rules.rx_if.dplx_line_edc_mode = CS_HSIO_EDC_MODE_SR;
    rules.rx_if.dplx_host_edc_mode = CS_HSIO_EDC_MODE_SR;

    /* Configuring the trace loss settings */
    /* The driver trace loss may need further tuning for the target application */
    /* The equalizer trace loss is currently only used in SR mode or when the */
    /* microcode is disabled */
    rules.tx_if.dplx_line_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.tx_if.dplx_host_driver.traceloss = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_line_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
    rules.rx_if.dplx_host_eq.traceloss     = CS_HSIO_TRACE_LOSS_2dB;
		
		rules.enable_fec   = TRUE;

    for(slice = 0; slice < CS4224_MAX_NUM_SLICES(0); slice=slice+2)
    {
      rules.polarity_inv = slice_inv[slice];  
			status |= cs4224_slice_enter_operational_state(slice, &rules);
    }

    return status;
}



/**
 * The following example describes the process of obtaining
 * the link status on multiple slices at once
 *
 * @return CS_OK on success, CS_ERROR on failure
 */
cs_status example_link_ready()
{
    cs_status status = CS_OK;

    /* if an interface isn't locked, this will tell you which one is having issues */
    cs_uint16  interface_fault = 0;
    /* true if all links are ready */
    cs_boolean links_ready = FALSE;
    
    /* There are two different examples here, one to check a few slices and one to check
     * all of them. We use a '#if' here to choose between them
     */
#if 0
    /* the number of interfaces to check */
    /* NOTE: duplex have 2 Rx interfaces per port (line/host) */
    cs_uint16  length = CS4224_MAX_NUM_CS4343_PORTS * 2;

    /* the interfaces var will list all the interfaces you want to check for lock */
    cs4224_interface_t interfaces[length];

    cs_uint16 i = 0;

    /* populate the interface list, in this case add everything */
    for(i = 0; i < length; i++)
    {
        /* each slice has a line and host Rx */
        interfaces[i].slice = i / 2;
        if(i%2)
        {
            interfaces[i].mseq_id = CS4224_DPLX_HOST_MSEQ;
        }
        else
        {
            interfaces[i].mseq_id = CS4224_DPLX_LINE_MSEQ;
        }
    }
#else
    /* the interfaces var will list all the interfaces you want to check for lock */
    cs4224_interface_t interfaces[] = { \
        { 6, CS4224_DPLX_LINE_MSEQ }, \
        { 6, CS4224_DPLX_HOST_MSEQ }, \
        { 7, CS4224_DPLX_LINE_MSEQ }, \
        { 7, CS4224_DPLX_HOST_MSEQ } \
    };

    /* always use automatic length determination to minimize bugs */
    cs_uint16 length = sizeof(interfaces)/sizeof(interfaces[0]);
#endif
    
    status |= cs4224_query_links_ready(interfaces, length, 500, &interface_fault, &links_ready);
    
    if(!links_ready)
    {
        cs_uint32 slice = interfaces[interface_fault].slice;
        e_cs4224_mseq_id mseq_id = interfaces[interface_fault].mseq_id;
        cs_char8  *side;
        
        if     (CS4224_DPLX_HOST_MSEQ == mseq_id) side = "HOST";
        else if(CS4224_DPLX_LINE_MSEQ == mseq_id) side = "LINE";
        else                                      side = "SPLX";
        
      //CS_TRACE(("ERROR: Interface %x %s is not locked\n",slice,side));
        return CS_ERROR;
    }
    
    return CS_OK;
}


/**
 * The following example shows how to wait for a number of interfaces to lock
 * before sending data through the EDC
 *
 * @return CS_OK on success, CS_ERROR on failure
 */
cs_status example_wait_link_ready()
{
    /* There are two different examples here, one to check a few slices and one to check
     * all of them. We use a '#if' here to choose between them
     */
#if 0
    /* the number of interfaces to check */
    /* NOTE: duplex have 2 Rx interfaces per port (line/host) */
    cs_uint16  length = CS4224_MAX_NUM_CS4343_PORTS * 2;

    /* the interfaces var will list all the interfaces you want to check for lock */
    cs4224_interface_t interfaces[length];

    cs_uint16 i = 0;

    /* populate the interface list, in this case add everything */
    for(i = 0; i < length; i++)
    {
        /* each slice has a line and host Rx */
        interfaces[i].slice = i / 2;
        if(i%2)
        {
            interfaces[i].mseq_id = CS4224_DPLX_HOST_MSEQ;
        }
        else
        {
            interfaces[i].mseq_id = CS4224_DPLX_LINE_MSEQ;
        }
    }
#else
    /* the interfaces var will list all the interfaces you want to check for lock */
    cs4224_interface_t interfaces[] = { \
        { 6, CS4224_DPLX_LINE_MSEQ }, \
        { 6, CS4224_DPLX_HOST_MSEQ }, \
        { 7, CS4224_DPLX_LINE_MSEQ }, \
        { 7, CS4224_DPLX_HOST_MSEQ } \
    };

    /* always use automatic length determination to minimize bugs */
    cs_uint16 length = sizeof(interfaces)/sizeof(interfaces[0]);
#endif
    
    if(CS_OK != cs4224_wait_for_links_ready(interfaces, length, 500, 10000))
    {
      //CS_TRACE(("ERROR: Interfaces are not locked, exiting...\n"));
        return CS_ERROR;
    }
    
    /* start traffic, etc... */
    
    return CS_OK;
}


/**
 * The following example describes the process of configuring
 * loopbacks.
 */

cs_status example_loopbacks()
{
    cs_status status = CS_OK;
    cs4224_rules_t rules; 
    cs4224_diags_duplex_loopback_state_t duplex_loopback_state;
    int slice;

 //CS_PRINTF(("Initializing the chip in 10.3125G mode\n"));

    /* Provision all ports of the chip for 10.3125G mode. This
     * assumes:
     *   - Device configured for 156.25MHz reference clock
     *   - No fractional divider
     *   - Microcode programmed automatically if not
     *     already programmed via EEPROM.
     */ 
    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);

    for(slice = 0; slice < CS4224_MAX_NUM_SLICES(0); slice++)
    {
        status |= cs4224_slice_enter_operational_state(slice, &rules);
    }
 
 //CS_PRINTF(("Apply Duplex loopbacks\n"));
    /* 
     * The API supports Duplex loopbacks on the duplex chips only.
     * Duplex Near loopbacks cannot co-exist with Duplex Far loopbacks 
     * ie if an attempt is made to apply a Line Duplex Near loopback while 
     * a Line Duplex Far loopback is applied, the request will be refused 
     * with an error message. Line and Host loopbacks are independant of 
     * each other.
     */
    /* Setup a Line Duplex Near loopback on slice 3 */ 
    status = cs4224_diags_duplex_loopback_enable(
                 3, 
                 CS4224_LOOPBK_DUPLEX_NEAR_DATA, 
                 CS4224_LOOPBK_LINE,
                 &duplex_loopback_state);

    /* Tear down the Line Duplex Near loopback */
    status = cs4224_diags_duplex_loopback_disable(
                 3, 
                 CS4224_LOOPBK_DUPLEX_NEAR_DATA, 
                 CS4224_LOOPBK_LINE,   
                 &duplex_loopback_state);

    return status;
}

cs_status cs4343_mon_clock_config()
{
	cs_status status = CS_OK;
	cs4224_pp_clk_mon_cfg_t clk_mon_cfg;
	cs4224_gbl_clk_mon_cfg_t gbl_clk_mon_cfg;
	
	gbl_clk_mon_cfg.clksel_divider = CS4224_CLK_MON_DIV1;
	gbl_clk_mon_cfg.clksel_src = CS4224_CLK_MON_GBL_REFCLK;//CS4224_CLK_MON_GBL_PP0ING ;//
	gbl_clk_mon_cfg.go = FALSE;
	gbl_clk_mon_cfg.free_run = TRUE;
	//gbl_clk_mon_cfg.max_thresh_0
	status |=cs4224_gbl_clock_monitor(0, &gbl_clk_mon_cfg);
	
	cs4224_pp_clk_mon_cfg_init(&clk_mon_cfg);
	
	clk_mon_cfg.clksel_divider = CS4224_CLK_MON_DIV1;
	clk_mon_cfg.clksel_src = CS4224_CLK_MON_PP_SRX;//CS4224_CLK_MON_PP_STX;
	clk_mon_cfg.go = FALSE;
	clk_mon_cfg.free_run = TRUE;
	clk_mon_cfg.duration = 0x4000;
	clk_mon_cfg.min_thresh = 0;
	clk_mon_cfg.max_thresh = 0;
	
	 status |=cs4224_pp_clock_monitor(0,CS4224_CFG_LINE_SIDE,&clk_mon_cfg);

	return status;
}


/**
 * The following example describes the process of configuring the PRBS
 * generator and checker.
 */
cs_status cs4343_diag_prbs7_checker(int chan,int invert)
{
    cs_status status = CS_OK;
    //cs4224_rules_t rules;
    cs_uint32  prbs_error_count;
    //cs_boolean prbs_sync;

    cs_uint32 slice;

   	switch(chan)
	{
		case 0:
			mdiochan = 0;
		  slice = 2;
			break;
		case 1:
			mdiochan = 0;
		  slice = 4;invert =! invert;			 
			break;
		case 2:
			mdiochan = 1;
		  slice = 2;
			break;
		case 3:
			mdiochan = 1;
		  slice = 4;invert =! invert;	
			break;
	}

    /* configure the PRBS checker on slice 0 */
    if(invert) status |= cs4224_diags_prbs_checker_config(
                  slice,    
                  CS4224_PRBS_LINE_INTERFACE, 
                  CS4224_PRBS_Tx_2exp7, 
                  TRUE, 
                  FALSE);
		else status |= cs4224_diags_prbs_checker_config(
                  slice,    
                  CS4224_PRBS_LINE_INTERFACE, 
                  CS4224_PRBS_Tx_2exp7, 
                  FALSE, 
                  FALSE);
//status |= cs4224_diags_prbs_checker_autopol_enable(  slice,  CS4224_PRBS_LINE_INTERFACE, FALSE);

    /* Turn on the PRBS checker on slice 0 */
    status |= cs4224_diags_prbs_checker_enable(
                  slice,    
                  CS4224_PRBS_LINE_INTERFACE, 
                  TRUE);

//		status |= cs4224_diags_prbs_checker_autopol_enable(  slice,  CS4224_PRBS_LINE_INTERFACE, 1);
    /* clear any initial PRBS errors */
    status |= cs4224_diags_prbs_checker_get_errors(
                  slice,
                  CS4224_PRBS_LINE_INTERFACE,
                  &prbs_error_count);
		//}


    return status;
}

cs_status cs4343_diag_prbs7_generator(int chan, int invert)
{
    cs_status status = CS_OK;
    //cs4224_rules_t rules;
    cs_uint32  prbs_error_count;
    //cs_boolean prbs_sync;

    cs_uint32 slice;

    	switch(chan)
	{
		case 0:
			mdiochan = 0;
		  slice = 2;
			break;
		case 1:
			mdiochan = 0;
		  slice = 4;
			break;
		case 2:
			mdiochan = 1;
		  slice = 2;
			break;
		case 3:
			mdiochan = 1;
		  slice = 4;
			break;
	}
 //CS_PRINTF(("Configuring the PRBS generator and checker\n"));

    //for(slice=0;slice<8;slice=slice+2)
    /* configure the PRBS generator on slice 0 and
     * put it in PFD mode */
    //{
			status |= cs4224_diags_prbs_generator_set_pfd_mode(
                  slice,
                  CS4224_PRBS_LINE_INTERFACE,
                  TRUE);

    if(invert)
			status |= cs4224_diags_prbs_generator_config(
                  slice,  
                  CS4224_PRBS_LINE_INTERFACE, 
                  CS4224_PRBS_Tx_2exp7, 
                  TRUE);
		else
			status |= cs4224_diags_prbs_generator_config(
                  slice,  
                  CS4224_PRBS_LINE_INTERFACE, 
                  CS4224_PRBS_Tx_2exp7, 
                  FALSE);

    return status;
}

cs_status cs4343_diag_prbs9_checker(int chan,int invert)
{
    cs_status status = CS_OK;
    //cs4224_rules_t rules;
    cs_uint32  prbs_error_count;
    //cs_boolean prbs_sync;

    cs_uint32 slice;

			switch(chan)
	{
		case 0:
			mdiochan = 0;
		  slice = 2;
			break;
		case 1:
			mdiochan = 0;
		  slice = 4;invert =! invert;	
			break;
		case 2:
			mdiochan = 1;
		  slice = 2;
			break;
		case 3:
			mdiochan = 1;
		  slice = 4;invert =! invert;	
			break;
	}

    /* configure the PRBS checker on slice 0 */
   if(invert) status |= cs4224_diags_prbs_checker_config(
                  slice,    
                  CS4224_PRBS_LINE_INTERFACE, 
                  CS4224_PRBS_Tx_2exp9, 
                  TRUE, 
                  FALSE);
	 else status |= cs4224_diags_prbs_checker_config(
                  slice,    
                  CS4224_PRBS_LINE_INTERFACE, 
                  CS4224_PRBS_Tx_2exp9, 
                  FALSE, 
                  FALSE);

//status |= cs4224_diags_prbs_checker_autopol_enable(  slice,  CS4224_PRBS_LINE_INTERFACE, FALSE);
    /* Turn on the PRBS checker on slice 0 */
    status |= cs4224_diags_prbs_checker_enable(
                  slice,    
                  CS4224_PRBS_LINE_INTERFACE, 
                  TRUE);

//		status |= cs4224_diags_prbs_checker_autopol_enable(  slice,  CS4224_PRBS_LINE_INTERFACE, 1);
    /* clear any initial PRBS errors */
    status |= cs4224_diags_prbs_checker_get_errors(
                  slice,
                  CS4224_PRBS_LINE_INTERFACE,
                  &prbs_error_count);
							//	}

    return status;
}

cs_status cs4343_diag_prbs9_generator(int chan,int invert)
{
    cs_status status = CS_OK;
    //cs4224_rules_t rules;
    cs_uint32  prbs_error_count;
    //cs_boolean prbs_sync;

    cs_uint32 slice;

			switch(chan)
	{
		case 0:
			mdiochan = 0;
		  slice = 2;
			break;
		case 1:
			mdiochan = 0;
		  slice = 4;
			break;
		case 2:
			mdiochan = 1;
		  slice = 2;
			break;
		case 3:
			mdiochan = 1;
		  slice = 4;
			break;
	}
 //CS_PRINTF(("Configuring the PRBS generator and checker\n"));

  //  for(slice=0;slice<8;slice=slice+2)
	//{
    /* configure the PRBS generator on slice 0 and
     * put it in PFD mode */
    status |= cs4224_diags_prbs_generator_set_pfd_mode(
                  slice,
                  CS4224_PRBS_LINE_INTERFACE,
                  TRUE);

    if(invert)
			status |= cs4224_diags_prbs_generator_config(
                  slice,  
                  CS4224_PRBS_LINE_INTERFACE, 
                  CS4224_PRBS_Tx_2exp9, 
                  TRUE);
		else
			status |= cs4224_diags_prbs_generator_config(
                  slice,  
                  CS4224_PRBS_LINE_INTERFACE, 
                  CS4224_PRBS_Tx_2exp9, 
                  FALSE);


    return status;
}

cs_status cs4343_diag_prbs15_checker(int chan,int invert)
{
    cs_status status = CS_OK;
    //cs4224_rules_t rules;
    cs_uint32  prbs_error_count;
    //cs_boolean prbs_sync;

    cs_uint32 slice;
		switch(chan)
	{
		case 0:
			mdiochan = 0;
		  slice = 2;
			break;
		case 1:
			mdiochan = 0;
		  slice = 4;invert =! invert;	
			break;
		case 2:
			mdiochan = 1;
		  slice = 2;
			break;
		case 3:
			mdiochan = 1;
		  slice = 4;invert =! invert;	
			break;
	}
 
    /* configure the PRBS checker on slice 0 */
   if(invert) status |= cs4224_diags_prbs_checker_config(
                  slice,    
                  CS4224_PRBS_LINE_INTERFACE, 
                  CS4224_PRBS_Tx_2exp15, 
                  TRUE, 
                  FALSE);
	 else  status |= cs4224_diags_prbs_checker_config(
                  slice,    
                  CS4224_PRBS_LINE_INTERFACE, 
                  CS4224_PRBS_Tx_2exp15, 
                  FALSE, 
                  FALSE);

//status |= cs4224_diags_prbs_checker_autopol_enable(  slice,  CS4224_PRBS_LINE_INTERFACE, FALSE);
    /* Turn on the PRBS checker on slice 0 */
    status |= cs4224_diags_prbs_checker_enable(
                  slice,    
                  CS4224_PRBS_LINE_INTERFACE, 
                  TRUE);

//		status |= cs4224_diags_prbs_checker_autopol_enable(  slice,  CS4224_PRBS_LINE_INTERFACE, 1);
    /* clear any initial PRBS errors */
    status |= cs4224_diags_prbs_checker_get_errors(
                  slice,
                  CS4224_PRBS_LINE_INTERFACE,
                  &prbs_error_count);
						//		}

    return status;
}

cs_status cs4343_diag_prbs15_generator(int chan,int invert)
{
    cs_status status = CS_OK;
    //cs4224_rules_t rules;
    cs_uint32  prbs_error_count;
    //cs_boolean prbs_sync;

    cs_uint32 slice;
			switch(chan)
	{
		case 0:
			mdiochan = 0;
		  slice = 2;
			break;
		case 1:
			mdiochan = 0;
		  slice = 4;
			break;
		case 2:
			mdiochan = 1;
		  slice = 2;
			break;
		case 3:
			mdiochan = 1;
		  slice = 4;
			break;
	}

 //CS_PRINTF(("Configuring the PRBS generator and checker\n"));

   // for(slice=0;slice<8;slice=slice+2)
	//{
    /* configure the PRBS generator on slice 0 and
     * put it in PFD mode */
    status |= cs4224_diags_prbs_generator_set_pfd_mode(
                  slice,
                  CS4224_PRBS_LINE_INTERFACE,
                  TRUE);

    if(invert)
			status |= cs4224_diags_prbs_generator_config(
                  slice,  
                  CS4224_PRBS_LINE_INTERFACE, 
                  CS4224_PRBS_Tx_2exp15, 
                  TRUE);
		else
			status |= cs4224_diags_prbs_generator_config(
											slice,  
											CS4224_PRBS_LINE_INTERFACE, 
											CS4224_PRBS_Tx_2exp15, 
											FALSE);

    return status;
}

cs_status cs4343_diag_prbs23_checker(int chan,int invert)
{
    cs_status status = CS_OK;
    //cs4224_rules_t rules;
    cs_uint32  prbs_error_count;
    //cs_boolean prbs_sync;

    cs_uint32 slice;

			switch(chan)
	{
		case 0:
			mdiochan = 0;
		  slice = 2;
			break;
		case 1:
			mdiochan = 0;
		  slice = 4;invert =! invert;	
			break;
		case 2:
			mdiochan = 1;
		  slice = 2;
			break;
		case 3:
			mdiochan = 1;
		  slice = 4;invert =! invert;	
			break;
	}

    /* configure the PRBS checker on slice 0 */
   if(invert) status |= cs4224_diags_prbs_checker_config(
                  slice,    
                  CS4224_PRBS_LINE_INTERFACE, 
                  CS4224_PRBS_Tx_2exp23, 
                  TRUE, 
                  FALSE);
	 else  status |= cs4224_diags_prbs_checker_config(
                  slice,    
                  CS4224_PRBS_LINE_INTERFACE, 
                  CS4224_PRBS_Tx_2exp23, 
                  FALSE, 
                  FALSE);
//status |= cs4224_diags_prbs_checker_autopol_enable(  slice,  CS4224_PRBS_LINE_INTERFACE, FALSE);

    /* Turn on the PRBS checker on slice 0 */
    status |= cs4224_diags_prbs_checker_enable(
                  slice,    
                  CS4224_PRBS_LINE_INTERFACE, 
                  TRUE);
//		status |= cs4224_diags_prbs_checker_autopol_enable(  slice,  CS4224_PRBS_LINE_INTERFACE, 1);

    /* clear any initial PRBS errors */
    status |= cs4224_diags_prbs_checker_get_errors(
                  slice,
                  CS4224_PRBS_LINE_INTERFACE,
                  &prbs_error_count);
	//	}

    return status;
}

cs_status cs4343_diag_prbs23_generator(int chan,int invert)
{
    cs_status status = CS_OK;
    //cs4224_rules_t rules;
    cs_uint32  prbs_error_count;
    //cs_boolean prbs_sync;

    cs_uint32 slice;

			switch(chan)
	{
		case 0:
			mdiochan = 0;
		  slice = 2;
			break;
		case 1:
			mdiochan = 0;
		  slice = 4;
			break;
		case 2:
			mdiochan = 1;
		  slice = 2;
			break;
		case 3:
			mdiochan = 1;
		  slice = 4;
			break;
	}
 //CS_PRINTF(("Configuring the PRBS generator and checker\n"));

    //for(slice=0;slice<8;slice=slice+2)
	//{
    /* configure the PRBS generator on slice 0 and
     * put it in PFD mode */
    status |= cs4224_diags_prbs_generator_set_pfd_mode(
                  slice,
                  CS4224_PRBS_LINE_INTERFACE,
                  TRUE);

    if(invert)
			status |= cs4224_diags_prbs_generator_config(
                  slice,  
                  CS4224_PRBS_LINE_INTERFACE, 
                  CS4224_PRBS_Tx_2exp23, 
                  TRUE);
		else			
		  status |= cs4224_diags_prbs_generator_config(
                  slice,  
                  CS4224_PRBS_LINE_INTERFACE, 
                  CS4224_PRBS_Tx_2exp23, 
                  FALSE);


    return status;
}

cs_status cs4343_diag_prbs31_checker(int chan,int invert)
{
    cs_status status = CS_OK;
    //cs4224_rules_t rules;
    cs_uint32  prbs_error_count;
    //cs_boolean prbs_sync;

    cs_uint32 slice;

		//slice =chan*2;
	switch(chan)
	{
		case 0:
			mdiochan = 0;
		  slice = 2;
			break;
		case 1:
			mdiochan = 0;
		  slice = 4;invert =! invert;		  
			break;
		case 2:
			mdiochan = 1;
		  slice = 2;
			break;
		case 3:
			mdiochan = 1;
		  slice = 4;invert =! invert;
			break;
	}

    /* configure the PRBS checker on slice 0 */
    if(invert) status |= cs4224_diags_prbs_checker_config(
                  slice,    
                  CS4224_PRBS_LINE_INTERFACE, 
                  CS4224_PRBS_Tx_2exp31, 
                  TRUE, 
                  FALSE);
		else status |= cs4224_diags_prbs_checker_config(
                  slice,    
                  CS4224_PRBS_LINE_INTERFACE, 
                  CS4224_PRBS_Tx_2exp31, 
                  FALSE, 
                  FALSE);


    /* Turn on the PRBS checker on slice 0 */
    status |= cs4224_diags_prbs_checker_enable(
                  slice,    
                  CS4224_PRBS_LINE_INTERFACE, 
                  TRUE);

//	status |= cs4224_diags_prbs_checker_autopol_enable(  slice,  CS4224_PRBS_LINE_INTERFACE, 1);
    /* clear any initial PRBS errors */
    status |= cs4224_diags_prbs_checker_get_errors(
                  slice,
                  CS4224_PRBS_LINE_INTERFACE,
                  &prbs_error_count);

 // }
    return status;	
}

cs_status cs4343_diag_prbs31_generator(int chan,int invert)
{
    cs_status status = CS_OK;
    //cs4224_rules_t rules;
    cs_uint32  prbs_error_count;
    //cs_boolean prbs_sync;

    cs_uint32 slice;
	
	switch(chan)
	{
		case 0:
			mdiochan = 0;
		  slice = 2;
			break;
		case 1:
			mdiochan = 0;
		  slice = 4;
			break;
		case 2:
			mdiochan = 1;
		  slice = 2;
			break;
		case 3:
			mdiochan = 1;
		  slice = 4;
			break;
	}

		//slice =chan*2;
 //CS_PRINTF(("Configuring the PRBS generator and checker\n"));

   // for(slice=0;slice<8;slice=slice+2)
	//{
    /* configure the PRBS generator on slice 0 and
     * put it in PFD mode */
    status |= cs4224_diags_prbs_generator_set_pfd_mode(
                  slice,
                  CS4224_PRBS_LINE_INTERFACE,
                  TRUE);

	if(invert) status |= cs4224_diags_prbs_generator_config(
                  slice,  
                  CS4224_PRBS_LINE_INTERFACE, 
                  CS4224_PRBS_Tx_2exp31, 
                  TRUE);
	else
    status |= cs4224_diags_prbs_generator_config(
                  slice,  
                  CS4224_PRBS_LINE_INTERFACE, 
                  CS4224_PRBS_Tx_2exp31, 
                  FALSE);

    return status;
}

cs_status cs4343_diag_prbs58_checker(int chan,int invert)
{
    cs_status status = CS_OK;
    //cs4224_rules_t rules;
    cs_uint32  prbs_error_count;
    //cs_boolean prbs_sync;

    cs_uint32 slice;

  	switch(chan)
	{
		case 0:
			mdiochan = 0;
		  slice = 2;
			break;
		case 1:
			mdiochan = 0;
		  slice = 4;invert =! invert;	
			break;
		case 2:
			mdiochan = 1;
		  slice = 2;
			break;
		case 3:
			mdiochan = 1;
		  slice = 4;invert =! invert;	
			break;
	}

    /* configure the PRBS checker on slice 0 */
    if(invert) status |= cs4224_diags_prbs_checker_config(
                  slice,    
                  CS4224_PRBS_LINE_INTERFACE, 
                  CS4224_PRBS_Tx_2exp58, 
                  TRUE, 
                  FALSE);
		status |= cs4224_diags_prbs_checker_config(
                  slice,    
                  CS4224_PRBS_LINE_INTERFACE, 
                  CS4224_PRBS_Tx_2exp58, 
                  FALSE, 
                  FALSE);

//status |= cs4224_diags_prbs_checker_autopol_enable(  slice,  CS4224_PRBS_LINE_INTERFACE,FALSE);
    /* Turn on the PRBS checker on slice 0 */
    status |= cs4224_diags_prbs_checker_enable(
                  slice,    
                  CS4224_PRBS_LINE_INTERFACE, 
                  TRUE);

//		status |= cs4224_diags_prbs_checker_autopol_enable(  slice,  CS4224_PRBS_LINE_INTERFACE, 1);
    /* clear any initial PRBS errors */
    status |= cs4224_diags_prbs_checker_get_errors(
                  slice,
                  CS4224_PRBS_LINE_INTERFACE,
                  &prbs_error_count);

  //}
    return status;
}


cs_status cs4343_diag_prbs58_generator(int chan, int invert)
{
    cs_status status = CS_OK;
    //cs4224_rules_t rules;
    cs_uint32  prbs_error_count;
    //cs_boolean prbs_sync;

    cs_uint32 slice;

  	switch(chan)
	{
		case 0:
			mdiochan = 0;
		  slice = 2;
			break;
		case 1:
			mdiochan = 0;
		  slice = 4;
			break;
		case 2:
			mdiochan = 1;
		  slice = 2;
			break;
		case 3:
			mdiochan = 1;
		  slice = 4;
			break;
	}
 //CS_PRINTF(("Configuring the PRBS generator and checker\n"));

   // for(slice=0;slice<8;slice=slice+2)
	// { /* configure the PRBS generator on slice 0 and
  //   * put it in PFD mode */
    status |= cs4224_diags_prbs_generator_set_pfd_mode(
                  slice,
                  CS4224_PRBS_LINE_INTERFACE,
                  TRUE);

    if(invert)
			status |= cs4224_diags_prbs_generator_config(
                  slice,  
                  CS4224_PRBS_LINE_INTERFACE, 
                  CS4224_PRBS_Tx_2exp58, 
                  TRUE);
		else
			status |= cs4224_diags_prbs_generator_config(
                  slice,  
                  CS4224_PRBS_LINE_INTERFACE, 
                  CS4224_PRBS_Tx_2exp58, 
                  FALSE);


    return status;
}

cs_status cs4343_diag_prbs9_5_checker(int  chan,int invert)
{
    cs_status status = CS_OK;    
    cs_uint32  prbs_error_count;
    //cs_boolean prbs_sync;

    cs_uint32 slice;

			switch(chan)
	{
		case 0:
			mdiochan = 0;
		  slice = 2;
			break;
		case 1:
			mdiochan = 0;
		  slice = 4;invert =! invert;	
			break;
		case 2:
			mdiochan = 1;
		  slice = 2;
			break;
		case 3:
			mdiochan = 1;
		  slice = 4;invert =! invert;	
			break;
	}

    /* configure the PRBS checker on slice 0 */
   if(invert) status |= cs4224_diags_prbs_checker_config(
                  slice,    
                  CS4224_PRBS_LINE_INTERFACE, 
                  CS4224_PRBS_Tx_2exp9_5, 
                  TRUE, 
                  FALSE);
	 else status |= cs4224_diags_prbs_checker_config(
                  slice,    
                  CS4224_PRBS_LINE_INTERFACE, 
                  CS4224_PRBS_Tx_2exp9_5, 
                  FALSE, 
                  FALSE);

//status |= cs4224_diags_prbs_checker_autopol_enable(  slice,  CS4224_PRBS_LINE_INTERFACE, FALSE);
    /* Turn on the PRBS checker on slice 0 */
    status |= cs4224_diags_prbs_checker_enable(
                  slice,    
                  CS4224_PRBS_LINE_INTERFACE, 
                  TRUE);

//		status |= cs4224_diags_prbs_checker_autopol_enable(  slice,  CS4224_PRBS_LINE_INTERFACE, 1);
    /* clear any initial PRBS errors */
    status |= cs4224_diags_prbs_checker_get_errors(
                  slice,
                  CS4224_PRBS_LINE_INTERFACE,
                  &prbs_error_count);
		//		}

    return status;
}


cs_status cs4343_diag_prbs9_5_generator(int  chan,int invert)
{
    cs_status status = CS_OK;    
    cs_uint32  prbs_error_count;
    //cs_boolean prbs_sync;

    cs_uint32 slice;

			switch(chan)
	{
		case 0:
			mdiochan = 0;
		  slice = 2;
			break;
		case 1:
			mdiochan = 0;
		  slice = 4;
			break;
		case 2:
			mdiochan = 1;
		  slice = 2;
			break;
		case 3:
			mdiochan = 1;
		  slice = 4;
			break;
	}
 //CS_PRINTF(("Configuring the PRBS generator and checker\n"));

    //for(slice=0;slice<8;slice=slice+2)
	//{
    /* configure the PRBS generator on slice 0 and
     * put it in PFD mode */
    status |= cs4224_diags_prbs_generator_set_pfd_mode(
                  slice,
                  CS4224_PRBS_LINE_INTERFACE,
                  TRUE);
if(invert)     
	status |= cs4224_diags_prbs_generator_config(
                  slice,  
                  CS4224_PRBS_LINE_INTERFACE, 
                  CS4224_PRBS_Tx_2exp9_5, 
                  TRUE);
else
	status |= cs4224_diags_prbs_generator_config(
                  slice,  
                  CS4224_PRBS_LINE_INTERFACE, 
                  CS4224_PRBS_Tx_2exp9_5, 
                  FALSE);


    return status;
}

cs_status cs4343_diag_prbs_check()
{
    cs_status status = CS_OK;
//    cs4224_rules_t rules;
    cs_uint32  prbs_error_count;
    cs_boolean prbs_sync;

//    cs_uint32 slice;	  
	/* get the PRBS error count */
    status |= cs4224_diags_prbs_checker_get_status(
                  0,
                  CS4224_PRBS_HOST_INTERFACE,
                  &prbs_error_count,
                  &prbs_sync);

	   if (prbs_sync)
    {
     //CS_PRINTF(("PRBS synchronized, error count %d\n", prbs_error_count));
    }
    else
    {
     //CS_PRINTF(("PRBS not synchronized, error count %d\n", prbs_error_count));
    }
    return status;
}

cs_status example_prbs()
{
    cs_status status = CS_OK;
    cs4224_rules_t rules;
    cs_uint32  prbs_error_count;
    cs_boolean prbs_sync;

    cs_uint32 slice;

 //CS_PRINTF(("Initializing the chip in 10.3125G mode\n"));

    /* Provision all ports of the chip for 10.3125G mode. This
     * assumes:
     *   - Device configured for 156.25MHz reference clock
     *   - No fractional divider
     *   - Microcode programmed automatically if not
     *     already programmed via EEPROM.
     */ 
    status |= cs4224_rules_set_default(CS4224_TARGET_APPLICATION_10G, &rules);

    for(slice = 0; slice < CS4224_MAX_NUM_SLICES(0); slice++)
    {
        status |= cs4224_slice_enter_operational_state(slice, &rules);
    }

 //CS_PRINTF(("Configuring the PRBS generator and checker\n"));

    
    /* configure the PRBS generator on slice 0 and
     * put it in PFD mode */
    status |= cs4224_diags_prbs_generator_set_pfd_mode(
                  0,
                  CS4224_PRBS_HOST_INTERFACE,
                  TRUE);

    status |= cs4224_diags_prbs_generator_config(
                  0,  
                  CS4224_PRBS_HOST_INTERFACE, 
                  CS4224_PRBS_Tx_2exp7, 
                  FALSE);

    /* Turn on the PRBS generator on slice 0 */
    status |= cs4224_diags_prbs_generator_enable(
                  0,  
                  CS4224_PRBS_HOST_INTERFACE, 
                  TRUE);

    /* configure the PRBS checker on slice 0 */
    status |= cs4224_diags_prbs_checker_config(
                  0,    
                  CS4224_PRBS_HOST_INTERFACE, 
                  CS4224_PRBS_Tx_2exp7, 
                  FALSE, 
                  FALSE);


    /* Turn on the PRBS checker on slice 0 */
    status |= cs4224_diags_prbs_checker_enable(
                  0,    
                  CS4224_PRBS_HOST_INTERFACE, 
                  TRUE);


    /* clear any initial PRBS errors */
    status |= cs4224_diags_prbs_checker_get_errors(
                  0,
                  CS4224_PRBS_HOST_INTERFACE,
                  &prbs_error_count);

    /* delay for a few seconds */

    /* get the PRBS error count */
    status |= cs4224_diags_prbs_checker_get_status(
                  0,
                  CS4224_PRBS_HOST_INTERFACE,
                  &prbs_error_count,
                  &prbs_sync);


    if (prbs_sync)
    {
     //CS_PRINTF(("PRBS synchronized, error count %d\n", prbs_error_count));
    }
    else
    {
     //CS_PRINTF(("PRBS not synchronized, error count %d\n", prbs_error_count));
    }
    return status;
}


/**
 * The following example describes the process of dumping
 * all slices of the device registers
 */
cs_status example_register_dump()
{
    cs_uint32 slice = 0;

    /* Dump all registers of all slices */ 
    cs4224_diags_register_dump_asic(slice);

    return CS_OK;
}

/**
 * The following example demonstrates the process of dumping
 * a range of registers.
 *
 * @return CS_OK on success, CS_ERROR on failure.
 */
cs_status example_register_dump_range()
{
    cs_status status = CS_OK;
    cs_uint16 registers[20];
    cs_uint32 i;

    /* Dump a few of the global registers */
    status |= cs4224_diags_register_dump_range(0, 0, 20-1, registers);

    for(i = 0; i < 20; i++)
    {
     //CS_PRINTF(("0x%04x = 0x%04x\n", 0 + i, registers[i]));
    }

    /* Now dump a few port pair registers */
    status |= cs4224_diags_register_dump_range(0, 0x1000, 0x1000 + (20 - 1), registers);

    for(i = 0; i < 20; i++)
    {
     //CS_PRINTF(("0x%04x = 0x%04x\n", 0x1000 + i, registers[i]));
    }
    

    return status;
}


/**
 * Validate write access to a particular register by performing a walking
 * 1's and 0's test.
 *
 * @param slice         [I] - The slice of the device being accessed.
 * @param addr          [I] - The address of the register being accessed.
 * @param bitmask       [I] - A bitmask to mask out the read-only fields.
 * @param show_progress [I] - TRUE if progress messages should be displayed,
 *                            FALSE 
 *
 * @return CS_OK on success, CS_ERROR on failure.
 */
cs_status cs4224_validate_reg_writes(
    cs_uint32  slice,
    cs_uint32  addr,
    cs_uint16  bitmask,
    cs_boolean show_progress)
{
    cs_status status = CS_OK;

    cs_uint16 saved_value = 0;
    cs_uint16 new_value = 0;
    int i;

    /* Read the current value to save it for restoration */
    status |= cs4224_reg_get_channel(slice, addr, &saved_value);

    /* Perform a walking 1's test */
    if(show_progress)
    {
     //CS_PRINTF(("Walking 1's test on %x\n", addr));
    }

    for(i = 0; i < 16; i++)
    {
        cs_uint16 pattern = 1 << i;

        /* Only do the test if we're hitting a writable bit */
        if((1<<i) == ((1<<i) & bitmask))
        {
            status |= cs4224_reg_set_channel(slice, addr, pattern);
            status |= cs4224_reg_get_channel(slice, addr, &new_value);
            if(show_progress)
            {
             //CS_PRINTF(("  - wrote %x, read %x\n", pattern, new_value));
            }

            if(!((new_value & bitmask) == (pattern & bitmask)))
            {
             //CS_PRINTF(("    - failed walking 1's on bit %d of %x\n", i, addr));
                return CS_ERROR;
            }
        }
        else
        {
            if(show_progress)
            {
             //CS_PRINTF(("  - skipping bit %d - not writable\n", i));
            }
        }
    }

    /* Perform a walking 0's test */
    if(show_progress)
    {
     //CS_PRINTF(("Walking 0's test on %x\n", addr));
    }

    for(i = 0; i < 16; i++)
    {
        cs_uint16 pattern = ~(1<<i);
        
        /* Only do the test if we're hitting a writable bit */
        if((1<<i) == ((1<<i) & bitmask))
        {
            status |= cs4224_reg_set_channel(slice, addr, pattern & bitmask);
            status |= cs4224_reg_get_channel(slice, addr, &new_value);
            if(show_progress)
            {
             //CS_PRINTF(("  - wrote %x, read %x\n", pattern & bitmask, new_value));
            }

            if(!((new_value & bitmask) == (pattern & bitmask)))
            {
             //CS_PRINTF(("    - failed walking 0's on bit %d of %x\n", i, addr));
                return CS_ERROR;
            }
        }
        else
        {
            if(show_progress)
            {
             //CS_PRINTF(("  - skipping bit %d\n", i));
            }
        }
    }

    /* Now restore the original data */
    status |= cs4224_reg_set_channel(slice, addr, saved_value);

    return status; 
}


/** 
 * This method performs a sweep of the entire register set. It
 * performs a write test to ensure that the bits may be written
 * correctly and then restores the original value. This is a
 * destructive test in that the chip must be re-initialized after the
 * test is performed.
 * 
 * @param slice         [I] - The slice of the device being accessed.
 * @param show_progress [I] - TRUE if progress messages should be displayed,
 *                            FALSE 
 *
 * @return CS_OK on success, CS_ERROR on failure.
 */
cs_status example_register_sweep(
    cs_uint32  slice,
    cs_boolean show_progress)
{
    cs_status status = CS_OK;
    cs_uint32 die;

    /* Reset the ASIC to put it into a known state */
    cs4224_hard_reset(slice);
    CS_MDELAY(1000);

    die = cs4224_get_die_from_slice(slice);
    
    /* Use broadcast to enable all the clocks */
    status |= cs4224_reg_set(die, CS4224_GLOBAL_BROADCAST, 0x80);
    status |= cs4224_reg_set(die, CS4224_PP_LINE_LINEMISC_CLKEN,              0xFFFF);
    status |= cs4224_reg_set(die, CS4224_PP_LINE_LINEMISC_MPIF_RESET_DOTREG,  0);
    status |= cs4224_reg_set(die, CS4224_PP_LINE_LINEMISC_GIGEPCS_SOFT_RESET, 0);
    status |= cs4224_reg_set(die, CS4224_PP_LINE_SDS_DSP_MSEQ_POWER_DOWN_LSB, 0x0000);
    status |= cs4224_reg_set(die, CS4224_PP_LINE_SDS_DSP_MSEQ_POWER_DOWN_MSB, 0x0000);
    status |= cs4224_reg_set(die, CS4224_GLOBAL_BROADCAST,                    0x0);

    if(CS_OK != cs4224_validate_reg_writes(slice, 0x0002, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x0003, 0x001f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x0004, 0x001f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x0005, 0x001f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x0006, 0x001f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x0007, 0x001f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x0008, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x0009, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x000a, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x0011, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x0014, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x0015, 0x0f01, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x0018, 0x0003, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x0019, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x001a, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x001b, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x001c, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x001d, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x001e, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x001f, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x0020, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x0021, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x0022, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x0023, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x0024, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x0027, 0x003f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x0029, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x00fe, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x0100, 0x0ff3, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x0101, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x0102, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x0104, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x0105, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x0106, 0x0ff3, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x0107, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x0108, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x010a, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x010b, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x010c, 0x0ff3, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x010d, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x010e, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x0110, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x0111, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x0112, 0x0ff3, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x0113, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x0114, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x0116, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x0117, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x0118, 0x0ff3, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x0119, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x011a, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x011c, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x011d, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x011e, 0x0007, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x011f, 0x0007, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x0120, 0x0007, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x0121, 0x0007, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x0122, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x0123, 0x1fff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x0125, 0x001f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x0129, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x012b, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x012f, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x0133, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x0137, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x013b, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x013f, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x0143, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x0147, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x014b, 0x001f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x02e0, 0x007f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x02e1, 0x0f13, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x02e2, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x02e6, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x02e7, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x02e8, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x02e9, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x0300, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x0304, 0x0007, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x0307, 0x0003, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x030a, 0x0003, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x030c, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x030d, 0x3fff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x030e, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1008, 0x0003, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1009, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x100a, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x100b, 0xf183, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x100c, 0x0007, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x100d, 0x0003, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x100e, 0x83ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1010, 0x0007, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1012, 0x3f3f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1020, 0x001d, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1021, 0x18ed, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1022, 0x0007, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1023, 0xff7f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1024, 0xf7ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1025, 0x0003, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1026, 0x07f3, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1027, 0xc0ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1028, 0x3f03, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1029, 0x0007, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x102a, 0x0077, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x102b, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x102c, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x102d, 0x0f00, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x102e, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x102f, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1030, 0x000c, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1031, 0x0fff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1032, 0xe07f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1033, 0xe0ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1034, 0x007f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1035, 0x007f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1038, 0x0fff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1039, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x103a, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x103b, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x103c, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x103d, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x103e, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x103f, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1040, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1041, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1042, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1043, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1044, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1045, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1046, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1047, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1048, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1049, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x104a, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x104b, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x104c, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x104d, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x104e, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x104f, 0x1f1f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1053, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1055, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1056, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1057, 0x0007, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1058, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1059, 0x7fff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x105a, 0xffbf, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x105d, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x105f, 0x0073, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1062, 0x7f03, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1063, 0x7f7f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1064, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1065, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1066, 0x000f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1067, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x106a, 0xff0f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x106c, 0xff0f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x106e, 0x0003, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x106f, 0xff75, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1070, 0xff1f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1075, 0x0007, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1077, 0x0f03, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1078, 0x0706, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1079, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x107a, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x107d, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x107e, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1081, 0x000f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1082, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1083, 0x060a, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1084, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1085, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1086, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1087, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1088, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1089, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x108a, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x108b, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x108c, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x108d, 0x3131, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x108e, 0x001f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1091, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1092, 0xf0ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1093, 0x0007, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1094, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1096, 0xff77, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1097, 0x000f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1098, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1099, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x109a, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x109b, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x109c, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x109d, 0x0003, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x109e, 0x003f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x109f, 0x1000, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x10a0, 0x000b, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x10a1, 0x0007, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x10a2, 0xe0f0, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x10a3, 0x000f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x10a4, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x10a5, 0x0007, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x10a6, 0x010f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x10a7, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x10a8, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x10a9, 0x1fff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x10aa, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x10ab, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x10ac, 0xf13f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x10ad, 0x000f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x10ae, 0x1f73, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x10af, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x10b0, 0x00f0, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x10b1, 0x3fff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x10b2, 0x7ff7, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x10b3, 0x0003, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x10b4, 0x0f7c, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x10b5, 0x0707, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x10b6, 0x0707, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x10b7, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x10b8, 0xf7f1, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x10b9, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x10bb, 0x0c0f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x10bd, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x10c0, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x10c3, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x10c5, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x10c8, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x10ca, 0x0001, show_progress)){return CS_ERROR;}
    /* 0x1220 is too complicated, certain values will cause an MDIO timeout */
    /*if(CS_OK != cs4224_validate_reg_writes(slice, 0x1220, 0xffff, show_progress)){return CS_ERROR;}*/
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1221, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1222, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1225, 0x000f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1228, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1234, 0x007f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1238, 0x0007, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1239, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x123a, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x123b, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x123c, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x123e, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x123f, 0x000f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1240, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1243, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1244, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1249, 0x0003, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x124e, 0x0fff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1250, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1251, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1252, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1253, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1254, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1255, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1256, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1257, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1258, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1259, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x125a, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x125b, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x125c, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x125d, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x125e, 0x0f0f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x125f, 0x070f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1261, 0x0f3f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1262, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1263, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1264, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1265, 0x003f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1266, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1267, 0x0fff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1268, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1269, 0x003f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x126a, 0x7fff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x126b, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x126c, 0x0003, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1273, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1274, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1275, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1280, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1281, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1282, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1283, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1284, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1285, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1286, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1287, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1288, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1289, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x128a, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x128b, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x128c, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x128d, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x128e, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x128f, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1290, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1291, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1292, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1293, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1294, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1295, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1296, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1297, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1298, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1299, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x129a, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x129b, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x129c, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x129d, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x129e, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x129f, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x12a0, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x12a1, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x12a2, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x12a3, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x12a4, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x12a5, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x12a6, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x12a7, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x12a8, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x12a9, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x12aa, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x12ab, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x12ac, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x12ad, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x12ae, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x12af, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x12b0, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x12b1, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x12b2, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x12b3, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x12b4, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x12b5, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x12b6, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x12b7, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x12b8, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x12b9, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1320, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1321, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1322, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1323, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1324, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1325, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1326, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1327, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1328, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1329, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x132a, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x132b, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x132c, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x132d, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x132e, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x132f, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1330, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1331, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1332, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1333, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1334, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1335, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1336, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1337, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1338, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1339, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x133a, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x133b, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x133c, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x133d, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x133e, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x133f, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1340, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1341, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1342, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1343, 0x000f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1344, 0x0008, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1345, 0x0002, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1355, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1357, 0x0007, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1358, 0x000f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x135b, 0x1f0f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x135c, 0x1f1f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1420, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1421, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1422, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x142e, 0xdf87, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x142f, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1430, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1431, 0x7f7f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1432, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1433, 0x3f3f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1434, 0x007f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1435, 0x000f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1436, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1437, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1438, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x143a, 0x0003, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1441, 0x7f01, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1442, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1460, 0x5c10, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1472, 0xfffe, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1473, 0x001f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1481, 0x0f1f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x14a0, 0x8003, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x14a3, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x14a5, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x14b0, 0x8031, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x14b1, 0x03ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x14b2, 0x03ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x14b3, 0x03ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x14b4, 0x03ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x14b6, 0x0003, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x14b8, 0x001f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x14c0, 0x800b, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x14c1, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x14c2, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x14c3, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x14c4, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x14e0, 0xd089, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x14e4, 0x0700, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x14ed, 0xff3f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x14ee, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x14ef, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1500, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1501, 0x0003, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1503, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1507, 0x0003, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x150c, 0x3c7f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x150d, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x150e, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x150f, 0x0007, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1511, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1512, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1513, 0x000f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1514, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1515, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1516, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1517, 0x0fff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1518, 0x007e, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x151d, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x151e, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x151f, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1520, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1521, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1522, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1523, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1524, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1525, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1526, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1527, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1528, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1529, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x152a, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x152b, 0x000f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x152e, 0x003f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1532, 0x0003, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1538, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1540, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1542, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1543, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1556, 0x007e, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1557, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1558, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x155b, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x155f, 0x0003, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1563, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1564, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1565, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1580, 0x0003, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1581, 0x0003, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1583, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1584, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1589, 0xc003, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x158c, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x158d, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x158e, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x158f, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1590, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1591, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1592, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1593, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1594, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1595, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1596, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1597, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1598, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1599, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x159a, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x159b, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x159c, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x159d, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x159e, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x159f, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x15a3, 0x0fff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x15a4, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x15a8, 0x07ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x15ab, 0xffcf, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x15ad, 0x7fff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x15ae, 0x7fff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x15af, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x15c0, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x15c1, 0x11ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x15c3, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x15c8, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x15cb, 0x01f1, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1600, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1601, 0x0700, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1603, 0x7f7f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1604, 0x3f3f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1605, 0x3f3f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1606, 0x003f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1607, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1608, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1609, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x160a, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x160e, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1640, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1641, 0x0706, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1643, 0x0f0f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1644, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x164f, 0xfffb, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1650, 0x0f0f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1651, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1652, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1653, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1654, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1655, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1656, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1657, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1658, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1659, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x165a, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x165b, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x165c, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x165d, 0x17ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x165e, 0x17ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1660, 0x0f1f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1664, 0x0003, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1680, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1682, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x168a, 0x7fff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x168b, 0xff81, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x168c, 0x000f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1808, 0x0003, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1809, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x180a, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x180b, 0xf180, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x180e, 0x83ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1810, 0x0006, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1812, 0x0007, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1820, 0x001d, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1821, 0x18ed, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1822, 0x0007, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1823, 0xff7f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1824, 0xf7ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1825, 0x0003, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1826, 0x07f3, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1827, 0xc0ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1828, 0x3f03, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1829, 0x0007, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x182a, 0x0077, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x182b, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x182c, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x182d, 0x0f00, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x182e, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x182f, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1830, 0x000c, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1831, 0x0fff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1832, 0xe07f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1833, 0xe0ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1834, 0x007f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1835, 0x007f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1838, 0x0fff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1839, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x183a, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x183b, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x183c, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x183d, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x183e, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x183f, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1840, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1841, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1842, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1843, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1844, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1845, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1846, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1847, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1848, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1849, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x184a, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x184b, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x184c, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x184d, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x184e, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x184f, 0x1f1f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1853, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1855, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1856, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1857, 0x0007, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1858, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1859, 0x7fff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x185a, 0xffbf, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x185d, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x185f, 0x0073, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1862, 0x7f03, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1863, 0x7f7f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1864, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1865, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1866, 0x000f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1867, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x186a, 0xff0f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x186c, 0xff0f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x186e, 0x0003, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x186f, 0xff75, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1870, 0xff1f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1875, 0x0007, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1877, 0x0f03, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1878, 0x0706, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1879, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x187a, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x187d, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x187e, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1881, 0x000f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1882, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1883, 0x060a, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1884, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1885, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1886, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1887, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1888, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1889, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x188a, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x188b, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x188c, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x188d, 0x3131, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x188e, 0x001f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1891, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1892, 0xf0ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1893, 0x0007, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1894, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1896, 0xff77, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1897, 0x000f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1898, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1899, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x189a, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x189b, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x189c, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x189d, 0x0003, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x189e, 0x003f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x189f, 0x1000, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x18a0, 0x000b, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x18a1, 0x0007, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x18a2, 0xe0f0, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x18a3, 0x000f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x18a4, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x18a5, 0x0007, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x18a6, 0x010f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x18a7, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x18a8, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x18a9, 0x1fff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x18aa, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x18ab, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x18ac, 0xf13f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x18ad, 0x000f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x18ae, 0x1f73, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x18af, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x18b0, 0x00f0, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x18b1, 0x3fff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x18b2, 0x7ff7, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x18b3, 0x0003, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x18b4, 0x0f7c, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x18b5, 0x0707, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x18b6, 0x0707, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x18b7, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x18b8, 0xf7f1, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x18b9, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x18bb, 0x0c0f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x18bd, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x18c0, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x18c3, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x18c5, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x18c8, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x18ca, 0x0001, show_progress)){return CS_ERROR;}
    /* 0x1a20 is too complicated, certain values will cause an MDIO timeout */
    /*if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a20, 0xffff, show_progress)){return CS_ERROR;}*/
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a21, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a22, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a25, 0x000f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a28, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a34, 0x007f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a38, 0x0007, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a39, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a3a, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a3b, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a3c, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a3e, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a3f, 0x000f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a40, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a43, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a44, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a49, 0x0003, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a4e, 0x0fff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a50, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a51, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a52, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a53, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a54, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a55, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a56, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a57, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a58, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a59, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a5a, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a5b, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a5c, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a5d, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a5e, 0x0f0f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a5f, 0x070f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a61, 0x0f3f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a62, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a63, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a64, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a65, 0x003f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a66, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a67, 0x0fff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a68, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a69, 0x003f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a6a, 0x7fff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a6b, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a6c, 0x0003, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a73, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a74, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a75, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a80, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a81, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a82, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a83, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a84, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a85, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a86, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a87, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a88, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a89, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a8a, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a8b, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a8c, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a8d, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a8e, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a8f, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a90, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a91, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a92, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a93, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a94, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a95, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a96, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a97, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a98, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a99, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a9a, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a9b, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a9c, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a9d, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a9e, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1a9f, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1aa0, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1aa1, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1aa2, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1aa3, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1aa4, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1aa5, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1aa6, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1aa7, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1aa8, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1aa9, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1aaa, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1aab, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1aac, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1aad, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1aae, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1aaf, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1ab0, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1ab1, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1ab2, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1ab3, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1ab4, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1ab5, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1ab6, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1ab7, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1ab8, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1ab9, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1b20, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1b21, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1b22, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1b23, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1b24, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1b25, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1b26, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1b27, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1b28, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1b29, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1b2a, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1b2b, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1b2c, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1b2d, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1b2e, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1b2f, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1b30, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1b31, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1b32, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1b33, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1b34, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1b35, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1b36, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1b37, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1b38, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1b39, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1b3a, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1b3b, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1b3c, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1b3d, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1b3e, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1b3f, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1b40, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1b41, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1b42, 0x01ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1b43, 0x000f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1b44, 0x0008, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1b45, 0x0002, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1b55, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1b57, 0x0007, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1b58, 0x000f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1b5b, 0x1f0f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1b5c, 0x1f1f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1c20, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1c21, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1c22, 0x0001, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1c2e, 0xdf87, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1c2f, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1c30, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1c31, 0x7f7f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1c32, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1c33, 0x3f3f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1c34, 0x007f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1c35, 0x000f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1c36, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1c37, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1c38, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1c3a, 0x0003, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1c41, 0x7f01, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1c42, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1c60, 0x5c10, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1c81, 0x0f1f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1d00, 0x007f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1d01, 0x0f13, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1d02, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1d06, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1d07, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1d08, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1d09, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1d10, 0x007f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1d11, 0x0f13, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1d12, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1d16, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1d17, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1d18, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x1d19, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x5004, 0x000f, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x5007, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x5009, 0x00ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x500a, 0x07ff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x500b, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x500d, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x500e, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x500f, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x5010, 0xffff, show_progress)){return CS_ERROR;}
    if(CS_OK != cs4224_validate_reg_writes(slice, 0x5011, 0xffff, show_progress)){return CS_ERROR;}


    /* Reset the slice again after the test */
    status |= cs4224_hard_reset(slice);

    return status;
}


/**
 * The following example describes the process of dumping the status
 * of the device.
 */
cs_status example_status()
{
    cs_status status = CS_OK;

    /* Print the full status for all slices */
    status |= cs4224_diags_show_status(0, CS4224_MAX_NUM_SLICES(0)-1, CS4224_STATUS_ALL);

    return status;
}


/**
 * The following example describes the process of obtaining
 * the version information from the API.
 *
 * @return CS_OK on success, CS_ERROR on failure
 */
cs_status cs4343_version_info()
{
    cs_status status = CS_OK;
    char version[512]={0};

    /* Get the API version information */
    status |= cs4224_version(version, 500);
    printf("%s\n", version);
	  VIEWTECH_A01 (400,420,0x6022,0xf800,0x39c8,version[0]);
		VIEWTECH_A01 (430,420,0x6022,0xf800,0x39c8,version[1]);
		VIEWTECH_A01 (460,420,0x6022,0xf800,0x39c8,version[2]);
		VIEWTECH_A01 (490,420,0x6022,0xf800,0x39c8,version[3]);
		VIEWTECH_A01 (520,420,0x6022,0xf800,0x39c8,version[4]);
		VIEWTECH_A01 (550,420,0x6022,0xf800,0x39c8,version[5]);
		VIEWTECH_A01 (580,420,0x6022,0xf800,0x39c8,version[6]);
		VIEWTECH_A01 (610,420,0x6022,0xf800,0x39c8,version[7]);
	

    return CS_OK;
}

cs_status cs4343_sku_info()
{
    cs_status status = CS_OK;
    int sku=1;

    switch(cs4224_hw_id(1))
		{
			case CS4224_HW_CS4343:
			{
				sku=4343;
				break;
			}
			case CS4224_HW_CS4223:
			{
				sku=4223;
				break;
			}
			case CS4224_HW_CS4224:
			{
				sku=4224;
				break;
			}
				case CS4224_HW_CS4221:
			{
				sku=4221;
				break;
			}
				default:
			{
				sku=0;
				break;
			}

		}

	  VIEWTECH_A01 (400,420,0x6022,0xf800,0x39c8,sku);	

    return status;
}

cs_status cs4343_test_mdio()
{
	cs_status status = CS_OK;
	cs_uint16 reg_data1=0;
	cs_uint16 reg_data2=0;
	cs_uint16 reg_data3=0;
	mdiochan = 1;
	status|=cs4224_reg_get_channel(0, CS4224_GLOBAL_CHIP_ID_LSB_dft, &reg_data1);
	status|=cs4224_reg_get_channel(0, CS4224_GLOBAL_CHIP_ID_MSB_dft, &reg_data2);
	status|=cs4224_reg_get_channel(0, CS4224_GLOBAL_GT_10KHZ_REF_CLK_CNT0, &reg_data3);
//	status|=cs4224_reg_get_channel(0, CS4224_GLOBAL_CHIP_ID_LSB_dft, &reg_data1);
//	status|=cs4224_reg_get_channel(2, CS4224_GLOBAL_CHIP_ID_MSB_dft, &reg_data2);
	VIEWTECH_A01 (400,420,0x6022,0xf800,0x39c8,reg_data1);
	VIEWTECH_A01 (500,420,0x6022,0xf800,0x39c8,reg_data2);
	VIEWTECH_A01 (600,420,0x6022,0xf800,0x39c8,reg_data3);
//	if(reg_data1==0x3E5)
//		return CS_OK;
//	else
//		return CS_ERROR;	
	return status;
}




