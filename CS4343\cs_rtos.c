/** @file cs_rtos.c
 ****************************************************************************
 *
 * @brief
 *    This contains all the RTOS(like system calls) and environment      *
 *    related macro's and stub utilities which should be modified or     *
 *    filled in as suited to the customer environment. It is important   *
 *    that this customization or porting of the driver is done BEFORE    *
 *    making any attempt to compile or use the driver.                   *
 *
 ****************************************************************************
 *  * <AUTHOR>    This file contains information proprietary to Inphi Corporation, Inc.
 *    (Inphi). Any use or disclosure, in whole or in part, of this
 *    information to any unauthorized party, for any purposes other than that for
 *    which it is provided is expressly prohibited except as authorized by
 *    Inphi in writing. Inphi reserves its rights to pursue both civil and
 *    criminal penalties for copying or disclosure of this material without
 *    authorization. Inphi Corporation (R), INPHI (R) and the Inphi Logo
 *    are the trademarks or registered trademarks of Inphi Corporation, Inc.,
 *    and its subsidiaries in the U.S. and other countries. Any other
 *    product and company names are the trademarks of their respective owners.
 *
 *    Copyright (C) 2006-2015 Inphi Corporation, Inc. All rights reserved.
 *
 *    API Version Number: 3.7.8
 ****************************************************************************/
#include "cs_rtos.h"
#include "delay.h"

/* Include any necessary library files when building the driver */
#ifndef CS_DONT_USE_STDLIB
#    include <stdlib.h>	       /* for malloc(), free(), abs() */
#    include <string.h>        /* for memcpy()                */
#    include <stdarg.h>        /* for variable args           */
#    include <stdio.h>         /* for printf variants         */

//#    if !defined(_WINDOWS) && !defined(_WIN32) && !defined(_WIN64)
//#        include <arpa/inet.h> /* for ntohs, htons            */
//#    else
//#        include <WinSock2.h>
//#    endif
#endif /* CS_DONT_USE_STDLIB */

/* ANSI C doesn't declare these methods */
//int usleep(unsigned int usecs);


void CS_UDELAY(int usecs)
{
#ifdef CS_DONT_USE_STDLIB
    #error "TO DO: Cannot compile without defining CS_UDELAY() for your system in platform/cs_rtos.c"
#else
    Delay_Us(usecs);
#endif
}

void CS_MDELAY(int msecs)
{
    CS_UDELAY(msecs * 1000);
}

unsigned int CS_ABS(int value)
{
#ifdef CS_DONT_USE_STDLIB
    return (unsigned int) (value < 0 ? -value : value);
#else
    return (unsigned int) abs(value);
#endif
}

void *CS_MEMSET(void *p, int c, int n)
{
  char *pb = (char *) p;
  char *pbend = pb + n;
  while (pb != pbend) *pb++ = c;
  return p;
}


void *CS_MEMCPY(void *dst, const void *src, int n)
{
#ifdef CS_DONT_USE_STDLIB
    void *ret = dst;

    while (n--)
    {
      *(char *)dst = *(char *)src;
      dst = (char *) dst + 1;
      src = (char *) src + 1;
    }

    return ret;
#else
    return memcpy(dst, src, n);
#endif
}


int CS_STRLEN(const char *s)
{
  const char *eos = s;
  while (*eos++);
  return (int) (eos - s - 1);
}


char *CS_STRCAT(char *dest, const char *source)
{ 
  char *d = dest;
  while (*d) ++d;
  while ((*d++ = *source++) != '\0') ;
  return (dest);
}


char *CS_STRNCPY(char *dest, const char *source, int count)
{
  char *start = dest;

  while (count && (*dest++ = *source++)) 
		count--;
  if (count) while (--count) *dest++ = '\0';
  return start;
}

#ifndef CS_DONT_USE_STDLIB
#ifndef CS_TRACE_FUNCTION
/** Don't use this function directly, use CS_TRACE((x)) instead
 * @private
 */
void CS_TRACE_FUNCTION(const char *fmt, ...)
{
    va_list ap;
    va_start(ap, fmt);
    vfprintf(CS_TRACE_STREAM, fmt, ap);
    va_end(ap);
}
#endif
#endif

cs_status CS_VERIFY_ENDIANESS()
{
  cs_uint16 val = 0x2211 ;
  cs_uint8  low_byte = ((cs_uint8 *)&val)[0] ;

  if (low_byte == 0x22) {
#ifdef CS_BIG_ENDIAN
    return (CS_OK) ;
#else /* CS_LITTLE_ENDIAN */
    return (CS_ERROR) ;
#endif
  }
  else if (low_byte == 0x11) {
#ifdef CS_BIG_ENDIAN
    return (CS_ERROR) ;
#else /* CS_LITTLE_ENDIAN */
    return (CS_OK) ;
#endif
  }
  else {
    return (CS_ERROR) ;
  }
}


