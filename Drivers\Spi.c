#include "spi.h"

void Mpd_Spi_switch(void)
{
	GPIO_InitTypeDef GPIO_InitStructure;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT; 
	GPIO_InitStructure.GPIO_OType = GPIO_OType_PP; 
	GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;  
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_8;
	GPIO_Init(GPIOD, &GPIO_InitStructure);//???CS
	GPIO_ResetBits(GPIOD,GPIO_Pin_8);//???
}

//void Simulate_SPI_Init(void) 
//{

//	GPIO_InitTypeDef GPIO_InitStructure;
//    // Configure SCK and MOSI as output
//    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT; 
//	GPIO_InitStructure.GPIO_OType = GPIO_OType_PP; 
//	GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;  
//	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
//	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_13 |GPIO_Pin_15;
//	GPIO_Init(GPIOB, &GPIO_InitStructure);//???CS

//    // Configure MISO as input
//	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN; 
//    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_14;
//	GPIO_Init(GPIOB, &GPIO_InitStructure);//???CS
//}


void Mpd_Spi_init(void)
{
	SPI_InitTypeDef  SPI_InitStructure;
	GPIO_InitTypeDef GPIO_InitStructure;
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_SPI2, ENABLE);//??SPI2??

	GPIO_PinAFConfig(GPIOB, GPIO_PinSource13, GPIO_AF_SPI2);//sck
	GPIO_PinAFConfig(GPIOB, GPIO_PinSource14, GPIO_AF_SPI2);//miso
	GPIO_PinAFConfig(GPIOB, GPIO_PinSource15, GPIO_AF_SPI2);//mosi

	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF; 
	GPIO_InitStructure.GPIO_OType = GPIO_OType_PP; 
	GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;  
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz; 
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_13 | GPIO_Pin_14 | GPIO_Pin_15; 
	GPIO_Init(GPIOB, &GPIO_InitStructure);//???

	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT; 
	GPIO_InitStructure.GPIO_OType = GPIO_OType_PP; 
	GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;  
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_12;
	GPIO_Init(GPIOB, &GPIO_InitStructure);//???CS
	
	GPIO_SetBits(GPIOB,GPIO_Pin_12);

	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_12;
	GPIO_Init(GPIOH, &GPIO_InitStructure);//???????
	
	SPI_InitStructure.SPI_Direction = SPI_Direction_2Lines_FullDuplex;//?????
	SPI_InitStructure.SPI_BaudRatePrescaler=SPI_BaudRatePrescaler_16;//??????
	SPI_InitStructure.SPI_DataSize = SPI_DataSize_8b; //????
	SPI_InitStructure.SPI_CPOL = SPI_CPOL_Low;//???,????
	SPI_InitStructure.SPI_CPHA = SPI_CPHA_1Edge;//????????
	SPI_InitStructure.SPI_NSS = SPI_NSS_Soft;
	SPI_InitStructure.SPI_FirstBit = SPI_FirstBit_MSB; 
	SPI_InitStructure.SPI_CRCPolynomial = 7;
	SPI_InitStructure.SPI_Mode = SPI_Mode_Master;
	SPI_Init(SPI2, &SPI_InitStructure);
	SPI_Cmd(SPI2,ENABLE);  
}



uint8_t SPI_Senddata(uint8_t data)
{
	uint8_t recv=0;
	//??????
	while(SPI_I2S_GetFlagStatus(SPI2,SPI_I2S_FLAG_TXE)!= SET);
	SPI_I2S_SendData(SPI2,data);
	//????????
	while(SPI_I2S_GetFlagStatus(SPI2,SPI_I2S_FLAG_RXNE)!= SET);
	recv = SPI_I2S_ReceiveData(SPI2);
	return recv;
}

//????
uint16_t SPI_read_data(uint16_t addr)//??
{
    GPIO_ResetBits(GPIOB, GPIO_Pin_12);
    uint16_t recv = 0;
    uint16_t send_data = (addr << 1) | 0x01;
    //?????????
    while (SPI_I2S_GetFlagStatus(SPI2, SPI_I2S_FLAG_TXE) == RESET);
    SPI_I2S_SendData(SPI2, send_data >> 8);
    
    while (SPI_I2S_GetFlagStatus(SPI2, SPI_I2S_FLAG_TXE) == RESET);
    SPI_I2S_SendData(SPI2, (uint8_t)send_data);
    
    //?????????
    while (SPI_I2S_GetFlagStatus(SPI2, SPI_I2S_FLAG_RXNE) == RESET);
    recv = SPI_I2S_ReceiveData(SPI2);
    
    while (SPI_I2S_GetFlagStatus(SPI2, SPI_I2S_FLAG_RXNE) == RESET);
    recv |= (SPI_I2S_ReceiveData(SPI2) << 8);
	
	//????
    while (SPI_I2S_GetFlagStatus(SPI2, SPI_I2S_FLAG_TXE) == RESET);
    SPI_I2S_SendData(SPI2, 0x00);
    
    while (SPI_I2S_GetFlagStatus(SPI2, SPI_I2S_FLAG_TXE) == RESET);
    SPI_I2S_SendData(SPI2, 0x00);
    
    while (SPI_I2S_GetFlagStatus(SPI2, SPI_I2S_FLAG_RXNE) == RESET);
    recv = SPI_I2S_ReceiveData(SPI2);
    recv = (recv<<8);
    while (SPI_I2S_GetFlagStatus(SPI2, SPI_I2S_FLAG_RXNE) == RESET);
    recv |= SPI_I2S_ReceiveData(SPI2);
    
    GPIO_SetBits(GPIOB, GPIO_Pin_12);
    return recv;
}

//SPI????
void SPI_write_data(uint16_t addr,uint16_t data)//??
{
    GPIO_ResetBits(GPIOB, GPIO_Pin_12);
    uint16_t recv = 0;
    uint16_t send_addr = (addr << 1);
    //?????????
    while (SPI_I2S_GetFlagStatus(SPI2, SPI_I2S_FLAG_TXE) == RESET);
    SPI_I2S_SendData(SPI2, send_addr >> 8);
    
    while (SPI_I2S_GetFlagStatus(SPI2, SPI_I2S_FLAG_TXE) == RESET);
    SPI_I2S_SendData(SPI2, (uint8_t)send_addr);
    
    //?????????
    while (SPI_I2S_GetFlagStatus(SPI2, SPI_I2S_FLAG_RXNE) == RESET);
    recv = SPI_I2S_ReceiveData(SPI2);
    
    while (SPI_I2S_GetFlagStatus(SPI2, SPI_I2S_FLAG_RXNE) == RESET);
    recv |= (SPI_I2S_ReceiveData(SPI2) << 8);
	
	//????
    while (SPI_I2S_GetFlagStatus(SPI2, SPI_I2S_FLAG_TXE) == RESET);
    SPI_I2S_SendData(SPI2, (data>>8));
    
    while (SPI_I2S_GetFlagStatus(SPI2, SPI_I2S_FLAG_TXE) == RESET);
    SPI_I2S_SendData(SPI2, (uint8_t)data);
    
    while (SPI_I2S_GetFlagStatus(SPI2, SPI_I2S_FLAG_RXNE) == RESET);
    recv = SPI_I2S_ReceiveData(SPI2);

    while (SPI_I2S_GetFlagStatus(SPI2, SPI_I2S_FLAG_RXNE) == RESET);
    recv = SPI_I2S_ReceiveData(SPI2);
    
    GPIO_SetBits(GPIOB, GPIO_Pin_12);
}

void SPI_Write_Register(uint8_t address, uint8_t data)
{
    GPIO_ResetBits(GPIOB, GPIO_Pin_12);
    SPI_I2S_SendData(SPI1, address); 
    while (SPI_I2S_GetFlagStatus(SPI1, SPI_I2S_FLAG_TXE) == RESET);

    SPI_I2S_SendData(SPI1, data);
    while (SPI_I2S_GetFlagStatus(SPI1, SPI_I2S_FLAG_TXE) == RESET);
    while (SPI_I2S_GetFlagStatus(SPI1, SPI_I2S_FLAG_BSY) != RESET);

    GPIO_SetBits(GPIOB, GPIO_Pin_12);
}

void W5500_spi_init(void)
{
	SPI_InitTypeDef  SPI_InitStructure;
	GPIO_InitTypeDef GPIO_InitStructure;
	//RCC_APB2PeriphClockCmd(RCC_APB2Periph_SPI1, ENABLE);

	GPIO_PinAFConfig(GPIOA, GPIO_PinSource5, GPIO_AF_SPI1);//sck
	GPIO_PinAFConfig(GPIOA, GPIO_PinSource6, GPIO_AF_SPI1);//miso
	GPIO_PinAFConfig(GPIOA, GPIO_PinSource7, GPIO_AF_SPI1);//mosi
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF; 
	GPIO_InitStructure.GPIO_OType = GPIO_OType_PP; 
	GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_DOWN;  
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz; 
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_5 | GPIO_Pin_6 | GPIO_Pin_7; 
	GPIO_Init(GPIOA, &GPIO_InitStructure);//???

	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT; 
	GPIO_InitStructure.GPIO_OType = GPIO_OType_PP; 
	GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_DOWN;  
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_4;
	GPIO_Init(GPIOA, &GPIO_InitStructure);//???CS
	GPIO_SetBits(GPIOA,GPIO_Pin_4);
	
	SPI_InitStructure.SPI_Direction = SPI_Direction_2Lines_FullDuplex;//???SPI1
	SPI_InitStructure.SPI_DataSize = SPI_DataSize_8b;
	SPI_InitStructure.SPI_CPOL = SPI_CPOL_High;
	SPI_InitStructure.SPI_CPHA = SPI_CPHA_2Edge;
	SPI_InitStructure.SPI_NSS = SPI_NSS_Soft;
	SPI_InitStructure.SPI_BaudRatePrescaler = SPI_BaudRatePrescaler_2;
	SPI_InitStructure.SPI_FirstBit = SPI_FirstBit_MSB; 
	SPI_InitStructure.SPI_CRCPolynomial = 7;
	SPI_InitStructure.SPI_Mode = SPI_Mode_Master;
	SPI_Init(SPI1, &SPI_InitStructure);
	SPI_Cmd(SPI1,ENABLE);  
}
/**
  * @brief  ?1?????SPI??
  * @param  TxData ???????
  * @retval None
  */
void SPI_WriteByte(uint8_t TxData)
{	
	while((SPI1->SR&SPI_I2S_FLAG_TXE)==(uint16_t)RESET);	//??????		  
	SPI1->DR=TxData;	 	  									//????byte 
	while((SPI1->SR&SPI_I2S_FLAG_RXNE)==(uint16_t)RESET); //???????byte  
	SPI1->DR;	
}
/**
  * @brief  ?SPI????1????
  * @retval ?????
  */
uint8_t SPI_ReadByte(void)
{	
	while((SPI1->SR&SPI_I2S_FLAG_TXE)==(uint16_t)RESET);	//??????			  
	SPI1->DR=0xFF;	 	  										//???????????????? 
	while((SPI1->SR&SPI_I2S_FLAG_RXNE)==(uint16_t)RESET); //???????byte  	
	return SPI1->DR;	
}
/**
  * @brief  ?????
  * @retval None
  */
void SPI_CrisEnter(void)
{
	__set_PRIMASK(1);
}
/**
  * @brief  ?????
  * @retval None
  */
void SPI_CrisExit(void)
{
	__set_PRIMASK(0);
}

/**
  * @brief  ?????????
  * @retval None
  */
void SPI_CS_Select(void)
{
	GPIO_ResetBits(GPIOA,GPIO_Pin_4);
}

/**
  * @brief  ?????????
  * @retval None
  */
void SPI_CS_Deselect(void)
{
	GPIO_SetBits(GPIOA,GPIO_Pin_4);
}
