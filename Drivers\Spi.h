#ifndef __SPI_H__
#define __SPI_H__
#include "stm32f4xx.h"
#define W5500_RST GPIO_Pin_1  //reset ??
#define W5500_SCS GPIO_Pin_0  //
void W5500_spi_init(void);
void SPI_Configuration(void);
void SPI_WriteByte(uint8_t TxData);
uint8_t SPI_ReadByte(void);
void SPI_CrisEnter(void);
void SPI_CrisExit(void);
void SPI_CS_Select(void);
void SPI_CS_Deselect(void);

void Mpd_Spi_init(void);
uint16_t SPI_read_data(uint16_t addr);
void Mpd_Spi_switch(void);
void SPI_write_data(uint16_t addr,uint16_t data);//??
#endif
