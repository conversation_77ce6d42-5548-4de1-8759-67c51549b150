/** @file examples.h
 *****************************************************************************
 *
 * @brief
 *    This module provides examples of using the CS4224 API to interact
 *    with the device.
 *
 *****************************************************************************
 * <AUTHOR>    This file contains information proprietary to Inphi Corporation, Inc.
 *    (Inphi). Any use or disclosure, in whole or in part, of this
 *    information to any unauthorized party, for any purposes other than that for
 *    which it is provided is expressly prohibited except as authorized by
 *    Inphi in writing. Inphi reserves its rights to pursue both civil and
 *    criminal penalties for copying or disclosure of this material without
 *    authorization. Inphi Corporation (R), INPHI (R) and the Inphi Logo
 *    are the trademarks or registered trademarks of Inphi Corporation, Inc.,
 *    and its subsidiaries in the U.S. and other countries. Any other
 *    product and company names are the trademarks of their respective owners.
 *
 *    Copyright (C) 2006-2015 Inphi Corporation, Inc. All rights reserved.
 *
 *    API Version Number: 3.7.8
 ****************************************************************************/
#include "cs4224_api.h"


/* Connect to the eval board to run the examples */
cs_status cs_dbg_set_remote_server(const char* ip, int port);

/*=======================================
 * Device initialization examples
 *=====================================*/

cs_status cs4343_init_1g(int chan);
cs_status cs4343_init_1g_inv(int chan);
cs_status Rules_Set_1g(void);

cs_status cs4343_init_5g(void);

cs_status cs4343_init_7p5g(void);

cs_status cs4343_init_10g(int chan);
cs_status cs4343_init_10g_inv(int chan);
cs_status Rules_Set_10g(void);
cs_status Rules_Set_Rate(void);

cs_status cs4343_init_10gwan(int chan);
cs_status cs4343_init_10gwan_inv(int chan);
cs_status Rules_Set_10gwan(void);

cs_status cs4343_init_rxaui(int chan);
cs_status cs4343_init_rxaui_inv(int chan);
cs_status Rules_Set_rxaui(void);

cs_status cs4343_init_xaui(int chan);
cs_status cs4343_init_xaui_inv(int chan);
cs_status Rules_Set_xaui(void);

cs_status cs4343_init_15g(void);
cs_status cs4343_init_15g_inv(void);
cs_status Rules_Set_15g(void);

cs_status cs4343_init_oc12(void);
cs_status cs4343_init_oc12_inv(void);

cs_status cs4343_init_oc24(void);
cs_status cs4343_init_oc24_inv(void);

cs_status cs4343_init_oc48(int chan);
cs_status cs4343_init_oc48_inv(int chan);
cs_status Rules_Set_oc48(void);

cs_status cs4343_init_oc96(int chan);
cs_status cs4343_init_oc96_inv(int chan);
cs_status Rules_Set_oc96(void);

cs_status cs4343_init_oc192(void);
cs_status cs4343_init_oc192_inv(void);

cs_status cs4343_init_fcan(void);

cs_status cs4343_init_fc16g(void);
cs_status cs4343_init_fc16g_inv(void);
cs_status Rules_Set_fc16g(void);

cs_status cs4343_init_fc10g(int chan);
cs_status cs4343_init_fc10g_inv(int chan);
cs_status Rules_Set_fc10g(void);

cs_status cs4343_init_fc8g(int chan);
cs_status cs4343_init_fc8g_inv(int chan);
cs_status Rules_Set_fc8g(void);

cs_status cs4343_init_fc4g(int chan);
cs_status cs4343_init_fc4g_inv(int chan);
cs_status Rules_Set_fc4g(void);

cs_status cs4343_init_fc2g(int chan);
cs_status cs4343_init_fc2g_inv(int chan);
cs_status Rules_Set_fc2g(void);

cs_status cs4343_init_fc1g(void);
cs_status cs4343_init_fc1g_inv(void);

cs_status cs4343_configure_10g_infiniband(int chan);
cs_status cs4343_configure_10g_infiniband_inv(int chan);
cs_status Rules_Set_10ginfi(void);

cs_status Rules_Set_12g(void);

cs_status cs4343_configure_5g_infiniband(int chan);
cs_status cs4343_configure_5g_infiniband_inv(int chan);
cs_status Rules_Set_5ginfi(void);

cs_status cs4343_configure_odu2(void);
cs_status cs4343_configure_odu2_inv(void);

cs_status cs4343_configure_otu1(void);
cs_status cs4343_configure_otu1_inv(void);

cs_status cs4343_configure_otu1e(void);
cs_status cs4343_configure_otu1e_inv(void);

cs_status cs4343_configure_otu1f(void);
cs_status cs4343_configure_otu1f_inv(void);

cs_status cs4343_configure_otu2(int chan);
cs_status cs4343_configure_otu2_inv(int chan);
cs_status Rules_Set_otu2(void);

cs_status cs4343_configure_otu2e(int chan);
cs_status cs4343_configure_otu2e_inv(int chan);
cs_status Rules_Set_otu2e(void);

cs_status cs4343_configure_otu2f(int chan);
cs_status cs4343_configure_otu2f_inv(int chan);
cs_status Rules_Set_otu2f(void);

cs_status Rules_Set_Epon(void);
cs_status Rules_Set_Gpon(void);
cs_status Rules_Set_Cpon(void);
cs_status Rules_Set_XGpon(void);
cs_status Rules_Set_XGSpon(void);

cs_status Rules_Set_Cpri155(void);
cs_status Rules_Set_Cpri622(void);
cs_status Rules_Set_Cpri3(void);
cs_status Rules_Set_Cpri6(void);
cs_status Rules_Set_Cpri9(void);
cs_status Rules_Set_Cpri300(void);

cs_status cs4343_configure_otu3(void);
cs_status cs4343_configure_otu3_inv(void);

cs_status cs4343_configure_otu3e2(void);
cs_status cs4343_configure_otu3e2_inv(void);

cs_status cs4343_configure_cpri8(void);
cs_status cs4343_configure_cpri8_inv(void);

cs_status cs4343_configure_cpri7(void);
cs_status cs4343_configure_cpri7_inv(void);

cs_status cs4343_configure_cpri6(void);
cs_status cs4343_configure_cpri6_inv(void);

cs_status cs4343_configure_cpri5(void);
cs_status cs4343_configure_cpri5_inv(void);

cs_status cs4343_configure_cpri4(void);
cs_status cs4343_configure_cpri4_inv(void);

cs_status cs4343_configure_cpri3(void);
cs_status cs4343_configure_cpri3_inv(void);

cs_status cs4343_configure_cpri2(void);
cs_status cs4343_configure_cpri2_inv(void);

cs_status cs4343_configure_cpri1(void);
cs_status cs4343_configure_cpri1_inv(void);

cs_status cs4343_mon_clock_config(void);

cs_status cs4343_diag_prbs9_5_generator(int chan, int invert);
cs_status cs4343_diag_prbs9_generator(int chan, int invert);
cs_status cs4343_diag_prbs7_generator(int chan, int invert);
cs_status cs4343_diag_prbs15_generator(int chan, int invert);
cs_status cs4343_diag_prbs23_generator(int chan, int invert);
cs_status cs4343_diag_prbs31_generator(int chan, int invert);
cs_status cs4343_diag_prbs58_generator(int chan, int invert);

cs_status cs4343_diag_prbs9_5_checker(int chan,int invert);
cs_status cs4343_diag_prbs9_checker(int chan,int invert);
cs_status cs4343_diag_prbs7_checker(int chan,int invert);
cs_status cs4343_diag_prbs15_checker(int chan,int invert);
cs_status cs4343_diag_prbs23_checker(int chan,int invert);
cs_status cs4343_diag_prbs31_checker(int chan,int invert);
cs_status cs4343_diag_prbs58_checker(int chan,int invert);

cs_status cs4343_diag_prbs_check(void);

cs_status example_init_10g(void);
cs_status example_init_1g(void);
cs_status example_init_fracdiv_10p3125g(void);
cs_status example_init_fc8g(void);
cs_status example_init_fcan(void);
cs_status example_init_fracdiv_11p5g(void);
cs_status example_init_kran(void);
cs_status example_init_kran_tpm(void);
cs_status example_init_switch_dyn_rate(void);
cs_status example_init_2x2_switch(void);
cs_status example_init_dynamic_reinit(void);
cs_status example_init_eeprom(void);

/*=======================================
 * Diagnostic Methods
 *=====================================*/
cs_status example_register_dump(void);
cs_status example_register_dump_range(void);
//cs_status example_register_sweep(void);
cs_status example_register_sweep(cs_uint32  slice, cs_boolean show_progress);
cs_status example_loopbacks(void);
cs_status example_prbs(void);

cs_status cs4343_version_info(void);
cs_status cs4343_sku_info(void);
cs_status cs4343_test_mdio(void);

cs_status example_link_ready(void);
cs_status example_wait_link_ready(void);
cs_status example_status(void);
cs_status example_eye_monitor(void);
cs_status example_eye_size(void);



/*=======================================
 * Examples of managing interrupts
 *=====================================*/
cs_status example_interrupts_receive_lock_status(void);
cs_status example_interrupts_gpio(void);
cs_status example_interrupts_mseq_ps(void);

